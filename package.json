{"name": "dante-frontend-app-v3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "concurrently --kill-others -p \"{name}\" -n \"app,storybook \" -c \"cyan.bold,green.bold,yellow.bold,magenta.bold\" \"npm run start:app\" \"npm run start:storybook\"", "start:app": "node server.dev.js", "start:bubble-embed-script": "vite build --mode development --watch --config vite-bubble-embed.config.js", "start:avatar-bubble-embed-script": "vite build --mode development --watch --config vite-avatar-bubble-embed.config.js", "start:storybook": "npm run storybook", "build": "cross-env rm -rf ./build && vite build --mode staging --config vite-avatar-bubble-embed.config.js && vite build --mode staging --config vite-bubble-embed.config.js && vite build", "build:production": "cross-env NODE_ENV=production rm -rf ./build && vite build --mode production --config vite-avatar-bubble-embed.config.js && vite build --mode production --config vite-bubble-embed.config.js && vite build --mode production", "build:staging": "cross-env NODE_ENV=staging rm -rf ./build && vite build --config vite-avatar-bubble-embed.config.js --mode staging && vite build --config vite-bubble-embed.config.js --mode staging && vite build --mode staging", "test": "npm run build", "format": "npx prettier --write './src/*/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "vite:dev": "vite", "vite:build": "vite build", "vite:preview": "vite preview", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_47d6e244a3f9f66", "test:unit": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@amplitude/analytics-browser": "^2.11.12", "@chromatic-com/storybook": "^3.2.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@microsoft/fetch-event-source": "^2.0.1", "@react-oauth/google": "^0.12.1", "@storybook/addon-essentials": "^8.3.6", "@storybook/addon-interactions": "^8.3.6", "@storybook/addon-links": "^8.3.6", "@storybook/addon-onboarding": "^8.3.6", "@storybook/addon-themes": "^8.3.6", "@storybook/blocks": "^8.3.6", "@storybook/react-vite": "^8.3.6", "@swc/plugin-styled-components": "^3.0.3", "amplitude-js": "^8.21.9", "axios": "^1.7.7", "chartjs-adapter-luxon": "^1.3.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "css": "^3.0.0", "dompurify": "^3.1.7", "downloadjs": "^1.4.7", "idb": "^8.0.0", "js-file-download": "^0.4.12", "lottie-colorify": "^0.8.0", "lottie-react": "^2.4.0", "lottie-web": "^5.12.2", "luxon": "^3.5.0", "protobufjs": "^7.4.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-confetti": "^6.4.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-katex": "^3.0.1", "react-markdown": "^9.0.1", "react-product-fruits": "^2.2.61", "react-router-dom": "^6.27.0", "react-router-prompt": "^0.7.2", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "storybook": "^8.3.6", "uuid": "^11.0.2", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.2.0", "w3c-css-validator": "^1.3.2", "wavesurfer.js": "^7.8.8", "zustand": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "@vitejs/plugin-react-swc": "^3.7.1", "cross-env": "^7.0.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "express": "^4.21.1", "globals": "^15.11.0", "nodemon": "^3.1.7", "tailwindcss": "^3.4.14", "vite": "^5.4.10"}}