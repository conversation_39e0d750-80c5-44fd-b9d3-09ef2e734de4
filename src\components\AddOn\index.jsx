import DButton from "../Global/DButton";
import DSelect from "../Global/DSelect";

const AddOn = ({ category, selectedMessages, setSelectedMessages, handlePurchase, loading }) => {
    return (
        <div className="w-full md:max-w-[300px] flex flex-col gap-size2">
            <p className="text-xl font-medium tracking-tight">{category.name}</p>
            <div className="flex gap-size1">
            {category.has_select ? (
                <div className="w-full flex gap-size1 items-center flex-wrap">
                    <p className="text-lg text-white text-opacity-75">Add</p>
                    <DSelect 
                        listButtonClass="!bg-transparent !border-white !border-opacity-75 !text-white !text-opacity-75 !text-sm !py-size0 !h-auto !w-full !max-w-56"
                        options={category.options} 
                        value={selectedMessages}
                        onChange={(value) => setSelectedMessages(value)}
                        selectedChild={
                            <p className="text-white/85 text-xs">{category.options.find(option => option.value === selectedMessages)?.label}</p>
                        }
                    />
                    <p className="text-base text-white/85">{category.description} {selectedMessages ? `${category.options.find(option => option.value === selectedMessages)?.price}$` : ''}</p>
                </div>
            ) : (
                <p className="text-base text-white/85">
                    <span dangerouslySetInnerHTML={{ __html: category.description }}></span>
                    {!category?.no_checkout && <b> {category?.price}$</b>}
                </p>
            )}
        </div>
        {category?.no_checkout ? (
            <DButton variant="grey" size="sm" className="bg-white/10 text-white/85 mt-auto" onClick={() => window.open('mailto:<EMAIL>', '_blank')}>
                Contact us
            </DButton>
        ):
        (<DButton variant="grey" size="sm" className="bg-white/10 text-white/85 mt-auto" loading={loading} onClick={() => handlePurchase(category.key === 'extra_messages' ? category.options.find(option => option.value === selectedMessages)?.path : category.path)}>
            Purchase Add-on
        </DButton>)
        }
    </div>
    )
}

export default AddOn;