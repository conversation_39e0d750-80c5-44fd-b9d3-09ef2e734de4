import React, { useState, useEffect, useRef } from 'react';
import WaveSurfer from 'wavesurfer.js';
import RecordPlugin from 'wavesurfer.js/dist/plugins/record.js';
import DButtonIcon from '../Global/DButtonIcon';
import MicrophoneIcon from '@/components/Global/Icons/MicrophoneIcon';
import StopIcon from '../Global/Icons/StopIcon';
import clsx from 'clsx';

const AudioRecorder = ({
  handleRecordingComplete,
  setQuestion,
  isRecording,
  setIsRecording,
  isPreviewMode,
}) => {
  // const [audioBlob, setAudioBlob] = useState(null);
  const wavesurferRef = useRef(null);
  const recognitionRef = useRef(null);
  const waveformRef = useRef(null);
  const recordPluginRef = useRef(null);

  useEffect(() => {
    // Create WaveSurfer instance
    const wavesurfer = WaveSurfer.create({
      container: waveformRef.current,
      waveColor: '#000',
      progressColor: '#09081F33',
      height: 30,
      cursorWidth: 0,
      barWidth: 4,
      barGap: 3,
      responsive: true,
      interact: false,
    });

    // Initialize record plugin
    const record = RecordPlugin.create({
      scrollingWaveform: true,
    });

    wavesurferRef.current = wavesurfer;
    recordPluginRef.current = record;
    wavesurferRef.current.registerPlugin(record);

    // Clean up
    return () => {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  const initializeSpeechRecognition = () => {
    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.error('Speech recognition not supported');
      return null;
    }
  
    const recognition = new SpeechRecognition();
    recognition.lang = 'en-US';
    recognition.interimResults = true;
    recognition.continuous = true;
  
    recognition.onresult = (event) => {
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; i++) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        }
      }
      setQuestion((prev) => prev + finalTranscript);
    };
  
    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
    };
  
    return recognition;
  };
  

  const startRecording = async () => {
    if (isPreviewMode) return;
    try {
      if (!wavesurferRef.current) return;
  
      await recordPluginRef.current.startRecording().then(() => {
        setIsRecording(true);
        const recognition = initializeSpeechRecognition();
        if (recognition) {
          recognitionRef.current = recognition;
          recognition.onend = () => {
            if (isRecording) {
              recognition.start();
            }
          };
          recognition.start();
        }
      });
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };
  
  const stopRecording = async () => {
    try {
      if (!wavesurferRef.current) return;

      // const blob = await recordPluginRef.current.stopRecording();
      // setAudioBlob(blob);
      setIsRecording(false);

      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }

      handleRecordingComplete(false);
    } catch (error) {
      console.error('Error stopping recording:', error);
    }
  };

  return (
    <div className="flex items-center gap-size1 py-[6px] relative">
      {!isRecording && (
        <DButtonIcon
          className={clsx("audio-recorder-button absolute top-0 left-0 bottom-0 my-auto bg-grey-5 !rounded-full !flex-shrink-0", {
            'cursor-default': isPreviewMode,
            '!size-7': true
          })}
          onClick={startRecording}
        >
          <MicrophoneIcon className="size-4"/>
        </DButtonIcon>
      )}
      <div className={`ml-[35px] grow text-grey-20 text-sm flex items-center !flex-shrink-0 ${isRecording ? 'hidden ml-0' : 'w-full'}`}>
        {!isRecording ? 'Start recording' : null}
      </div>
      <div id="waveform" ref={waveformRef} className="w-[87%] md:w-[95%] !flex-shrink-0"></div>
      {isRecording && (
        <DButtonIcon
          className="audio-recorder-button-stop bg-grey-5 !rounded-full"
          onClick={stopRecording}
        >
          <StopIcon />
        </DButtonIcon>
      )}
    </div>
  );
};

export default AudioRecorder;
