import DButton from "../Global/DButton";
import GoogleIcon from "../Global/Icons/GoogleIcon";
import StarIcon from "../Global/Icons/StarIcon";
import { useChatbotStore } from "@/stores/chatbot/chatbotStore";
import BgImage from "@/assets/modal/signup_popup_background.png"
import { useSearchParams } from "react-router-dom";

const BubblePopup = () => {
    //get search params that are after the ?
    const searchParams = new URLSearchParams(window.location.search);
    const kb_id = searchParams.get('kb_id');
    const token = searchParams.get('token');

    const setSaveSignUpBtn = useChatbotStore((state) => state.setSaveSignUpBtn);
  return  <div className="flex flex-col gap-size2 p-size2 mx-size1 mb-[10px] rounded-size1"
        style={{
            backgroundImage: `url(${BgImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
        }}
    >
        <div className="bg-black w-[32px] h-[32px] flex justify-center items-center rounded-size2 shadow-sm">
            <StarIcon  className="text-white" />
         </div>
        <p className="text-base font-medium">
            Access Your Free Dante AI Chatbot
        </p>
        <p className="text-sm text-grey-50">
            Your custom AI Chatbot will be saved in your Dante AI account and ready to use for free.
        </p>
        <div className="flex items-center gap-size1">
        <DButton
            fullWidth
            variant="dark"
            onClick={() => {
                window.open(`https://app.dante-ai.com/sign-up/?kb_id=${kb_id}&is_landing_page=True`, '_blank');
            }}
          >
            Sign Up with Email
          </DButton>
          <DButton
            fullWidth
            variant="dark"
            onClick={() => {
                window.open(`https://app.dante-ai.com/sign-up/?kb_id=${kb_id}&is_landing_page=True`, '_blank');
            }}
          >
            <GoogleIcon />
            Sign up with Google
          </DButton>

        </div>
    </div>
};

export default BubblePopup;
