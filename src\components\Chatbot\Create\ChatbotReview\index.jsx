import DAlert from '@/components/Global/DAlert';
import DInputEditable from '@/components/Global/DInputEditable';
import DButton from '@/components/Global/DButton';
import ReturnIcon from '@/components/Global/Icons/ReturnIcon';
import { useEffect } from 'react';
import TrainIcon from '@/components/Global/Icons/TrainIcon';
import FilesIcon from '@/components/Global/Icons/FilesIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import LinkIcon from '@/components/Global/Icons/LinkIcon';

const ChatbotReview = ({ chatbotData, setChatbotData, setActiveTab, setCurrentStep, onBackClick, onTrainClick }) => {
  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      onTrainClick();
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onTrainClick]);

  const handleClickSelection = (tab, step) => {
    setActiveTab(tab);
    setCurrentStep(step);
  };

  const handleChangeUrl = (e, index) => {
    const newUrls = [...chatbotData.chatbotUrls];
    newUrls[index].url = e.target.value;
    setChatbotData('chatbotUrls', newUrls);
  };

  const handleDeleteUrl = (index) => {
    const newUrls = [...chatbotData.chatbotUrls];
    newUrls.splice(index, 1);
    setChatbotData('chatbotUrls', newUrls);
  };

  const handleEditUrl = (index) => {
    // Create a prompt for the user to edit the URL
    const newUrl = prompt("Edit URL:", chatbotData.chatbotUrls[index].url);
    if (newUrl && newUrl.trim() !== '') {
      const newUrls = [...chatbotData.chatbotUrls];
      newUrls[index].url = newUrl.trim();
      setChatbotData('chatbotUrls', newUrls);
    }
  };

  const handleChangeExcludedUrl = (e, index) => {
    const newExcludedUrls = [...chatbotData.chatbotExcludedUrls];
    newExcludedUrls[index].url = e.target.value;
    setChatbotData('chatbotExcludedUrls', newExcludedUrls);
  };

  const handleDeleteExcludedUrl = (index) => {
    const newExcludedUrls = [...chatbotData.chatbotExcludedUrls];
    newExcludedUrls.splice(index, 1);
    setChatbotData('chatbotExcludedUrls', newExcludedUrls);
  };

  const handleChangeFile = (e, index) => {
    const newFiles = [...chatbotData.chatbotFiles];
    newFiles[index].url = e.target.value;
    setChatbotData('chatbotFiles', newFiles);
  };

  const handleDeleteFile = (index) => {
    const newFiles = [...chatbotData.chatbotFiles];
    newFiles.splice(index, 1);
    setChatbotData('chatbotFiles', newFiles);
  };

  const handleEditName = (value, index, state) => {
    switch (state) {
      case 'files':
        const newFiles = [...chatbotData.chatbotFiles];
        newFiles[index].name = value;
        setChatbotData('chatbotFiles', newFiles);
        break;
      case 'urls':
        const newUrls = [...chatbotData.chatbotUrls];
        newUrls[index].url = value;
        setChatbotData('chatbotUrls', newUrls);
        break;
      case 'excluded':
        const newExcludedUrls = [...chatbotData.chatbotExcludedUrls];
        newExcludedUrls[index].url = value;
        setChatbotData('chatbotExcludedUrls', newExcludedUrls);
        break;
    }
  };

  const handleChangeBulkUploadedUrl = (e, index) => {
    const newBulkUploadedUrls = [...chatbotData.chatbotBulkUploadedUrls];
    newBulkUploadedUrls[index].url = e.target.value;
    setChatbotData('chatbotBulkUploadedUrls', newBulkUploadedUrls);
  };

  const handleDeleteBulkUploadedUrl = (index) => {
    const newBulkUploadedUrls = [...chatbotData.chatbotBulkUploadedUrls];
    newBulkUploadedUrls.splice(index, 1);
    setChatbotData('chatbotBulkUploadedUrls', newBulkUploadedUrls);
  };

  return (
    <div>
      <div className="flex justify-end gap-size1">
        <DButton
          variant="outlined"
          size="md"
          fullWidth
          onClick={onBackClick}
          className='!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3'
        >
          Back
        </DButton>
        <DButton
          variant="contained"
          size="md"
          fullWidth
          onClick={onTrainClick}
          className='!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3 flex items-center justify-center '
        >
          <div className="flex items-center justify-center gap-1">
            <span className='text-white !font-bold text-lg'>Finish</span>
            <TrainIcon className="w-5 h-5" />
          </div>
        </DButton>
      </div>
    <div className="flex flex-col gap-size8 max-w-[650px] mx-auto pt-size5">
      <div className="flex flex-col gap-size1">
        <p className="text-xl font-medium tracking-tight">
          Review your settings below
        </p>
        <p className="text-grey-50 text-xs font-light tracking-tight">
          When you're ready, click Finish to build your AI Chatbot. You can still make changes after it's built.
        </p>
      </div>
      {/* <DAlert state="alert" className="!w-full !items-center">
        <p className="text-sm">
          Please review all the details below. Once everything is set as you want, click 'Train' to build your AI Chatbot. You can edit your AI Chatbot after training is complete.
        </p>
      </DAlert> */}
      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Name</p>
          <DInputEditable
            value={chatbotData.chatbotName}
            onChange={(e) => setChatbotData('chatbotName', e.target.value)}
            className="!px-0"
            paragraphClassName="!px-0"
          />
        </div>
        <div className="bg-grey-5 w-full h-px"></div>
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Memory files</p>
          {chatbotData.chatbotFiles.length === 0 && (
            <div className='flex flex-col gap-size1'>
              <p className='text-grey-50 text-xs font-light tracking-tight'>
                No files have been added.
              </p>
            </div>
          )}
          {chatbotData.chatbotFiles.length > 0 &&
            chatbotData.chatbotFiles.map((file, index) => (
              <div key={index} className="flex flex-col gap-size0 max-h-[300px] overflow-y-auto">
                <div className="flex flex-col">
                  <div className="px-size1 py-size0 flex items-center justify-between bg-grey-2 rounded-size1">
                    <div className="flex gap-size0 items-center">
                      <FilesIcon className="size-4" />
                      <span className="text-sm font-regular tracking-tight">
                        {file.name}
                      </span>
                    </div>
                    <button className="dbutton" onClick={() => handleDeleteFile(index)}>
                      <DeleteIcon className="size-4"/>
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
        <div className="bg-grey-5 w-full h-px"></div>

        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Memory URLs</p>
          {chatbotData.chatbotUrls.filter(url => url.url !== 'https://').length === 0 && (
              <p className='text-grey-50 text-xs font-light tracking-tight'>
                No URLs have been added.
              </p>
          )}
          {chatbotData.chatbotUrls.filter(url => url.url !== 'https://').map((url, index) => (
            <div key={index} className="flex flex-col gap-size0 max-h-[300px] overflow-y-auto">
              <div className="flex flex-col">
                <div className="px-size1 py-size0 flex items-center justify-between bg-grey-2 rounded-size1">
                  <div className="flex gap-size0 items-center">
                    <LinkIcon className="size-4" />
                    <span className="text-sm font-regular tracking-tight">
                      {url.url}
                    </span>
                  </div>
                  <button className="dbutton" onClick={() => handleDeleteUrl(index)}>
                    <DeleteIcon className="size-4"/>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* <div className="bg-grey-6 w-full h-px"></div>
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Excluded URLs</p>
          {chatbotData.chatbotExcludedUrls.filter(url => url.url !== 'https://').length === 0 && (
            <DAlert state="info" className="items-center !w-full">
              <p>
                No URLs have been added.{' '}
                <span
                  onClick={() => handleClickSelection('excludedUrls', 1)}
                  className="decoration-solid underline decoration-1 cursor-pointer"
                >
                  Click
                </span>{' '}
                here to jump back and add.
              </p>
            </DAlert>
          )}
          {chatbotData.chatbotExcludedUrls.filter(url => url.url !== 'https://').map((url, index) => (
            <DInputEditable
              key={index}
              value={url.url}
              onChange={(e) => handleChangeExcludedUrl(e, index)}
              is_editable
              is_deletable
              onDelete={() => handleDeleteExcludedUrl(index)}
              onEdit={(e) => handleEditName(e, index, 'excluded')}
            />
          ))}
        </div>
        <div className="bg-grey-6 w-full h-px"></div>
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Bulk uploaded URLs</p>
          {chatbotData.chatbotBulkUploadedUrls.length === 0 && (
            <DAlert state="info" className="items-center !w-full">
              <p>
                No URLs have been added.{' '}
                <span
                  onClick={() => handleClickSelection('bulkUploadedUrls', 1)}
                  className="decoration-solid underline decoration-1 cursor-pointer"
                >
                  Click
                </span>{' '}
                here to jump back and add.
              </p>
            </DAlert>
          )}
          {chatbotData.chatbotBulkUploadedUrls.filter(url => url.name !== 'https://').map((url, index) => (
            <DInputEditable
              key={index}
              value={url.name}
              onChange={(e) => handleChangeBulkUploadedUrl(e, index)}
              is_deletable
              onDelete={() => handleDeleteBulkUploadedUrl(index)}
            />
          ))}
        </div> */}
      </div>
    </div>
    </div>
  );
};

export default ChatbotReview;
