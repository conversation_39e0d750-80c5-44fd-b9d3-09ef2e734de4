import React, { useEffect, useState, useRef } from 'react';
import { Field, Radio, RadioGroup } from '@headlessui/react';

import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import DModal from '@/components/Global/DModal';
import DSpinner from '@/components/Global/DSpinner';
import DSwitch from '@/components/Global/DSwitch';
import DTooltip from '@/components/Global/DTooltip';
import DTransition from '@/components/Global/DTransition';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import { getShareToken, trackKlaviyoEvent } from '@/services/chatbot.service';
import { useUserStore } from '@/stores/user/userStore';
import checkEnv from '@/helpers/environmentCheck';
import { trackShareChatbot } from '@/helpers/analytics';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import * as chatbotService from '@/services/chatbot.service';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import DButtonIcon from '@/components/Global/DButtonIcon';

const methodShare = [
  {
    name: 'Chat Bubble',
    value: 'bubble',
    snippet_code:
      '<script src="<% BASE_URL %>bubble-embed.js?kb_id=<% KB_ID %>&token=<% TOKEN %>&modeltype=<% LLM_MODEL %>&tabs=<% TABS %>"></script>',
    snippet_code_old:
      '<script>window.danteEmbed = "<% BASE_URL %>embed/?kb_id=<% KB_ID %>&token=<% TOKEN %>&modeltype=<% LLM_MODEL %>&mode=false&logo=ZmFsc2U%3D&bubble=true&bubbleopen=false"</script><script src="<% BASE_URL %>bubble-embed.js"></script>',
  },
  {
    name: 'iFrame',
    value: 'iframe',
    snippet_code:
      '<iframe src="<% BASE_URL %>embed/?kb_id=<% KB_ID %>&token=<% TOKEN %>&modeltype=<% LLM_MODEL %>&tabs=<% TABS %>" allow="clipboard-write; clipboard-read; *;microphone *" width="100%" height="950" frameborder="0"></iframe>',
  },
  {
    name: 'Direct Link',
    value: 'direct-link',
    snippet_code:
      '<% BASE_URL %>embed/?kb_id=<% KB_ID %>&token=<% TOKEN %>&modeltype=<% LLM_MODEL %>',
  },
];

const getCopySuccessMessage = (method) => {
  switch (method) {
    case 'bubble':
      return "Copied! Add this code to your website's <head> tag to show the chat bubble";
    case 'iframe':
      return "Copied! Add this iframe where you want the chatbot to appear";
    case 'direct-link':
      return "Copied! Share this link with anyone to let them use your chatbot";
    default:
      return "Copied!";
  }
};

const DModalShareChatbot = ({
  isOpen,
  onClose,
  kb_id,
  llm_model,
  origin = '',
}) => {
  const [selectedMethod, setSelectedMethod] = useState(methodShare[0]);
  const [shareWithTabs, setShareWithTabs] = useState(true);
  const [snippetCode, setSnippetCode] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [shareToken, setShareToken] = useState(null);
  const [baseUrl, setBaseUrl] = useState('');
  const hasGeneratedToken = useRef(false);
  const [copyMessage, setCopyMessage] = useState('');

  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const chatbotCustomization = useCustomizationStore(
    (state) => state.chatbotCustomization
  );
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const llmModel = useChatbotStore((state) => state.selectedChatbot?.llmModel);

  // Effect to generate token only once when modal opens
  useEffect(() => {
    if (kb_id && llm_model && isOpen && !hasGeneratedToken.current) {
      const generateToken = async () => {
        setIsGenerating(true);
        try {
          // Track share clicked event
          const user = useUserStore.getState().user;
          if (checkEnv()) {
            trackShareChatbot({
              chatbot_id: kb_id,
              share_method: selectedMethod.value,
              user_id: user?.email,
            });
          }

          await trackKlaviyoEvent('share clicked', {
            chatbot_id: kb_id,
            share_method: selectedMethod.value,
          });

          const { data } = await getShareToken(kb_id, llm_model);
          setShareToken(data.token);

          // Set base URL
          const url = chatbotCustomization?.custom_url && chatbotCustomization?.custom_url_enabled
            ? `https://${chatbotCustomization?.custom_url}/`
            : selectedChatbot?.knowledge_base?.new_design_activated
            ? import.meta.env.VITE_APP_BASE_URL
            : import.meta.env.VITE_APP_BASE_URL_OLD_URL;
          setBaseUrl(url);

          hasGeneratedToken.current = true;
        } catch (error) {
          console.error('Error generating share token:', error);
        } finally {
          setIsGenerating(false);
        }
      };

      generateToken();
    }

    // Reset token generation state when modal is closed
    if (!isOpen) {
      hasGeneratedToken.current = false;
    }
  }, [isOpen, kb_id, llm_model, selectedChatbot, chatbotCustomization]);

  // Effect to update snippet code when tab changes or token is generated
  useEffect(() => {
    if (shareToken && baseUrl) {
      setIsGenerating(true);
      try {
        let snippet = selectedMethod.snippet_code;

        if (
          !selectedChatbot?.knowledge_base?.new_design_activated &&
          selectedMethod.value === 'bubble'
        ) {
          snippet = selectedMethod.snippet_code_old;
        }

        snippet = snippet
          .replaceAll('<% BASE_URL %>', baseUrl)
          .replaceAll('<% KB_ID %>', kb_id)
          .replaceAll('<% TOKEN %>', shareToken)
          .replaceAll('<% LLM_MODEL %>', llmModel ? llmModel.value : llm_model)
          .replaceAll('<% TABS %>', !shareWithTabs ? 'true' : 'false');

        setSnippetCode(snippet);
      } catch (error) {
        console.error('Error updating snippet code:', error);
      } finally {
        setIsGenerating(false);
      }
    }
  }, [
    selectedMethod,
    shareWithTabs,
    shareToken,
    baseUrl,
    kb_id,
    llm_model,
    llmModel,
    selectedChatbot,
  ]);

  const handleCopy = () => {
    navigator.clipboard.writeText(snippetCode);
    setIsCopied(true);
    setCopyMessage(getCopySuccessMessage(selectedMethod.value));

    setTimeout(() => {
      setIsCopied(false);
      setCopyMessage('');
    }, 3000);
  };

  const loadChatbotData = async () => {
    try {
      setIsGenerating(true);
      const { data } = await chatbotService.getChatbotById(kb_id);
      setSelectedChatbot(data?.results);
    } catch (e) {
      console.error('Error loading chatbot data:', e);
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    if (origin === 'dashboard' && kb_id) {
      loadChatbotData();
    }
  }, [origin, kb_id]);

  const handleClose = () => {
    // Reset states when modal is closed
    setSnippetCode('');
    onClose();
  };

  return (
    <DModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Share AI Chatbot"
      subtitle="Embed your AI chatbot on your website or share via a direct link."
      className="!rounded-2xl overflow-hidden bg-[oklch(97%_.001_286.4)] !min-w-[600px]"
      headerClassName="!gap-size1 !justify-center text-center w-full"
      titleClassName="!text-xl text-center w-full animate-fadeIn"
      subtitleClassName="!text-xs text-center w-full animate-fadeInUp"
      contentBgColor="pt-size5 bg-[oklch(97%_.001_286.4)]"
      // imageBgColor="bg-[#E4E1FF] rounded-xl"
      // imageSection={
      //   <img
      //     src="https://dante-public-files.lon1.cdn.digitaloceanspaces.com/Share%20image.png"
      //     alt="Share Chatbot"
      //     className="rounded-lg md:w-full md:h-auto animate-fadeIn size-[100px]"
      //   />
      // }
      line={false}
    >
      <div
        className="flex flex-col gap-size3 max-h-[85vh] overflow-y-hidden animate-fadeIn"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#ccc #f1f1f1',
        }}
      >
        <div className="sticky top-0 z-10 flex items-center justify-center pb-size1 pt-0 animate-fadeInUpDelayed1">
          <div className="inline-flex items-center gap-size2 px-size2 py-size1 rounded-full shadow-sm bg-white transition-all duration-300 hover:shadow-md">
            <RadioGroup
              value={selectedMethod}
              onChange={setSelectedMethod}
              defaultValue={methodShare[0]}
              aria-label="Select sharing method"
              className="flex flex-row justify-center gap-size3"
            >
              {methodShare.map((method) => (
                <Field key={method.name} className="flex items-center gap-2">
                  <Radio
                    value={method}
                    className={`transition-all duration-300 ease-in-out flex items-center justify-center gap-2 ${
                      selectedMethod.name === method.name
                        ? 'bg-grey-5 font-medium border-grey-5 scale-105'
                        : ''
                    } data-[checked]:bg-grey-5 data-[checked]:font-medium data-[checked]:text-black border border-transparent data-[checked]:border-purple-100 w-full cursor-pointer rounded-full px-size3 py-size1 text-sm tracking-[-2%] hover:scale-105`}
                  >
                    <span className="w-max">{method.name}</span>
                  </Radio>
                </Field>
              ))}
            </RadioGroup>
          </div>
        </div>

        <div className="flex flex-col gap-size2 bg-white p-size5 rounded-xl shadow-sm border border-grey-5 animate-fadeInUpDelayed2 relative">
          <div className="min-h-[30px] relative md:overflow-hidden">
            <DTransition
              show={selectedMethod.value === 'bubble'}
              key="bubble-description"
              className="absolute w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100"
            >
              <p className="text-sm text-grey-75 font-medium">
                Copy and paste this script into your <code>&lt;head&gt;</code> tag:
              </p>
            </DTransition>
            <DTransition
              show={selectedMethod.value === 'iframe'}
              key="iframe-description"
              className="absolute w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100"
            >
              <p className="text-sm text-grey-75 font-medium max-w-[90%]">
                Copy and paste this code into your HTML:
              </p>
            </DTransition>
            <DTransition
              show={selectedMethod.value === 'direct-link'}
              key="direct-link-description"
              className="absolute w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100"
            >
              <p className="text-sm text-grey-75 font-medium max-w-[90%]">
                Anyone can use your AI chatbot with this link.
              </p>
            </DTransition>
          </div>

          <div className="flex flex-col gap-size2 text-sm text-grey-75 font-light text-wrap min-h-[100px] relative overflow-hidden transition-all duration-300">
            <DTransition
              show={true}
              key={selectedMethod.value}
              className="w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100 animate-fadeIn"
            >
              {isGenerating ? (
                <div className="flex items-center justify-center h-full w-full">
                  <div className="animate-pulse-subtle transition-all duration-300">
                    <DSpinner
                      color="#8275f7"
                      style={{ height: 30, width: 30 }}
                    />
                  </div>
                </div>
              ) : (
                <div className="w-full break-words whitespace-normal">
                  <style>
                    @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300..700&display=swap');
                  </style>
                  <div style={{ fontFamily: "'Fira Code', monospace" }} className="text-sm">
                    {selectedMethod.value === 'direct-link' ? (
                      <a href={snippetCode} target="_blank" className="text-grey-75 hover:text-black transition-colors duration-300">
                        {snippetCode}
                      </a>
                    ) : (
                      snippetCode
                    )}
                  </div>
                </div>
              )}
            </DTransition>
          </div>
          <div className="flex justify-center animate-fadeInUpDelayed3 absolute top-[14px] right-[10px]">
            <DButton
              variant=""
              className={`copy-bot-link text-grey-50 hover:text-black text-sm py-size2 transition-all duration-300 !gap-size0 !items-start ${
                isCopied ? 'animate-pulse-subtle' : ''
              }`}
              onClick={handleCopy}
              data-chatbot-id={kb_id}
            >
              <CopyIcon
                className={`w-4 h-4 transition-all duration-300 ${
                  isCopied ? 'rotate-12' : ''
                }`}
              />
              <span className="text-sm mt-[0px]">{isCopied ? 'Copied!' : 'Copy'}</span>
            </DButton>
          </div>
          {copyMessage && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-green-10 text-black px-2 py-1 rounded-2xl text-xs animate-fadeIn whitespace-nowrap">
              {copyMessage}
            </div>
          )}
        </div>
        {/* <div className="relative overflow-hidden animate-fadeInUpDelayed3 min-h-[40px]">
          <DTransition
            show={selectedMethod.value === 'bubble'}
            key="bubble-footer"
            className="absolute w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100"
          >
            <p className="text-sm text-grey-50 t">
            This will add a chat bubble to your website. 
            </p>
          </DTransition>
          <DTransition
            show={selectedMethod.value === 'iframe'}
            key="iframe-footer"
            className="absolute w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100"
          >
            <p className="text-sm text-grey-50">
              This will integrate the chatbot into your site for a seamless and
              interactive experience.
            </p>
          </DTransition>
          <DTransition
            show={selectedMethod.value === 'direct-link'}
            key="direct-link-footer"
            className="absolute w-full transition-all duration-300 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-y-2 data-[closed]:scale-[0.98] data-[enter]:delay-100"
          >
            <p className="text-sm text-grey-50">
              Copy and share the link to let others interact with your AI
              Chatbot easily!
            </p>
          </DTransition>
        </div> */}

        {/*<DTransition show={selectedMethod.value === 'bubble'}>
          <div className="flex flex-col gap-size5">
            <div className="flex items-center gap-size2">
              <DSwitch
                checked={shareWithTabs}
                onChange={() => setShareWithTabs(!shareWithTabs)}
                label="Disable tabs"
              />
              <DTooltip content="Disable tabs to share without extra navigation options.">
                <InfoIcon />
              </DTooltip>
            </div>
            <DTransition show={!shareWithTabs}>
              <DAlert state="alert" fullWidth>
                Heads up! The AI Chatbot bubble will include tabs. To share as
                chat only, toggle above or disable tabs in the settings.
              </DAlert>
            </DTransition>
          </div>
        </DTransition> */}

        <div className="flex flex-col gap-0 justify-center items-center pb-size2 animate-fadeInUpDelayed3 mt-1">
          <p className="text-grey-50 text-xs">
            Customize the style of your AI chatbot <a href={`/chatbot/${kb_id}/styling`} target="_blank" className="underline text-grey-50 hover:text-purple-300">here</a>. 
          </p>
          <p className="text-grey-50 text-xs">
            Need help? Read our <a href="https://www.dante-ai.com/guides/how-to-share-your-ai-chatbot" target="_blank" className="underline text-grey-50 hover:text-purple-300">guide</a> or <a href="mailto:<EMAIL>" className="underline text-grey-50 hover:text-purple-300">email us</a>.
          </p>
        </div>
      </div>
    </DModal>
  );
};

export default DModalShareChatbot;
