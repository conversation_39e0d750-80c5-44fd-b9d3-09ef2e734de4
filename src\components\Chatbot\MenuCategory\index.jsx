import CategoryIcon from '@/components/Global/CategoryIcon';
import QuickResponseIcon from '@/components/Global/Icons/QuickResponseIcon';
import { updateCategoryConversation } from '@/services/humanHandover';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import { useEffect, useState } from 'react';

const MenuCategory = ({
  currentCategoryId,
  handleCategoryClick = () => {},
  view = 'conversation_box'
}) => {
  const { categories } = useHumanHandoverStore((state) => state);
  const [currentCategory, setCurrentCategory] = useState(null);
  
  useEffect(() => {
    const currentCategory = categories.find(
      (category) => category.id === currentCategoryId
    );
    setCurrentCategory(currentCategory);
  }, [currentCategoryId, categories]);

  return (
    <Menu as="div" className="relative">
      <MenuButton
        className={`border border-grey-10 size-6 flex items-center justify-center ${view === 'conversation' ? 'py-size1 px-size2 rounded-size1 border border-grey-5 gap-size1 text-grey-50 w-36 h-full' : ''}`}
      >
        <CategoryIcon style={{ color: currentCategory?.color }} />
        {view === 'conversation' && (
          <p className="text-sm font-medium">{currentCategory?.name}</p>
        )}
      </MenuButton>
      <MenuItems
        className="absolute z-10 mt-1 transition duration-150 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 data-[closed]:translate-y-2 bg-white rounded-size0 flex flex-col gap-size1 p-size1 translate-y-[-2px] !max-w-[500px] border border-grey-5 rounded-size1"
      >
        {currentCategory && (
          <MenuItem key="current">
            {({ active }) => (
              <button
                className={`flex gap-size1 items-center p-size1 w-full text-left border-b border-grey-10`}
                disabled={true}
              >
                <CategoryIcon style={{ color: currentCategory?.color }} />
                <h1 className="text-sm font-medium">{currentCategory?.name}</h1>
              </button>
            )}
          </MenuItem>
        )}
        {categories.filter(cat => cat.id !== currentCategoryId).map((category, i) => (
          <MenuItem key={category.id || i}>
            {({ active }) => (
              <button
                className={`flex gap-size1 items-center p-size1 w-full text-left ${active ? 'bg-grey-5' : ''}`}
                onClick={() => handleCategoryClick(category.id)}
              >
                <CategoryIcon style={{ color: category?.color }} />
                <h1 className="text-sm font-medium">{category?.name}</h1>
              </button>
            )}
          </MenuItem>
        ))}
      </MenuItems>
    </Menu>
  );
};

export default MenuCategory;
