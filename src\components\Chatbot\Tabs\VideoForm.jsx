import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import LinkIcon from "@/components/Global/Icons/LinkIcon";
import DInput from "@/components/Global/DInput/DInput";
import DSwitch from "@/components/Global/DSwitch";
import DButton from "@/components/Global/DButton";
import DSpinner from "@/components/Global/DSpinner";
import { getThumbnail } from "@/services/tabs.service";
import { v4 as uuidv4 } from 'uuid';

const VideoForm = ({ setIsSaving, closeModal, item, slots, setSlots, chatbotId }) => {
  const params = useParams();
  const [videoUrl, setVideoUrl] = useState(item?.url ?? 'https://');
  const [videoTitle, setVideoTitle] = useState(item?.title ?? '');
  const [videoDescription, setVideoDescription] = useState(item?.description ?? '');
  const [showThumbnail, setShowThumbnail] = useState(item?.show_thumbnail ?? false);
  const [thumbnailUrl, setThumbnailUrl] = useState(item?.thumbnail_url ?? 'https://');
  const [error, setError] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    let errors = {};

    if (videoUrl === '') {
      errors.videoUrl = 'Video URL is required';
    }
    if (videoTitle === '') {
      errors.videoTitle = 'Video title is required';
    }
    if (showThumbnail && thumbnailUrl === '') {
      errors.thumbnailUrl = 'Thumbnail URL is required';
    }

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    const payload = {
      kb_id: params.id ?? chatbotId,
      url: videoUrl,
      title: videoTitle,
      description: videoDescription,
      show_thumbnail: showThumbnail,
      thumbnail_url: thumbnailUrl,
      order: slots.length + 1,
      frontend_id: uuidv4(),
    }

    setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'video', disclaimer: 'Video' }]);
    closeModal();
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return; 

    setIsSaving(true);

    setSlots((prevSlots) => 
      prevSlots.map(slot => 
        (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? { 
          ...slot, 
          url: videoUrl, 
          title: videoTitle, 
          description: videoDescription, 
          show_thumbnail: showThumbnail, 
          thumbnail_url: thumbnailUrl,
          order: item.order,
          disclaimer: 'Video',
          frontend_id: item.frontend_id,
        } : slot
      )
    );

    closeModal();
    setIsSaving(false);
  };

  const handleGetThumbnail = async (checked) => {
    try{
      setIsLoading(true);
      const response = await getThumbnail(videoUrl);
      if(response.status === 200){
        setThumbnailUrl(response.data.thumbnail_url);
        setShowThumbnail(checked);
        setIsLoading(false);
      }
    }catch(error){
      console.error("Error getting thumbnail:", error);
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col gap-size5">
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video URL</p>
        <DInput 
          placeholder="Enter video URL" 
          iconPlacement="pre" 
          icon={<LinkIcon />} 
          value={videoUrl} 
          onChange={(e) => setVideoUrl(e.target.value)} 
          error={submitted ? error['videoUrl'] : ''}
        />
        <span className="text-xs text-grey-20 font-light">
          *YouTube, Vimeo or Loom link
        </span>
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video title</p>
        <DInput 
          placeholder="Enter video title" 
          value={videoTitle} 
          onChange={(e) => setVideoTitle(e.target.value)} 
          error={submitted ? error['videoTitle'] : ''}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video description</p>
        <DInput 
          placeholder="Enter video description" 
          minRows={5} 
          value={videoDescription} 
          onChange={(e) => setVideoDescription(e.target.value)} 
          error={submitted ? error['videoDescription'] : ''}
        />
      </div>
      <div className="flex items-center gap-size1 h-[30px]">
        <DSwitch label="Show thumbnail" checked={showThumbnail} 
        onChange={(checked) => handleGetThumbnail(checked)} />
        {isLoading && <DSpinner />}
      </div>
      {showThumbnail && (
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Thumbnail URL</p>
          <DInput 
            placeholder="Enter thumbnail URL" 
            value={thumbnailUrl} 
            onChange={(e) => setThumbnailUrl(e.target.value)} 
            error={submitted ? error['thumbnailUrl'] : ''}
          />
        </div>
      )}

      {item && Object.keys(item).length > 0 ? (
        <DButton variant="dark" onClick={handleUpdate} fullWidth>Update</DButton>
      ) : (
        <DButton variant="dark" onClick={handleSubmit} fullWidth>Complete</DButton>
      )}
    </div>
  );
};

export default VideoForm;
