import { useEffect, useState } from "react";
import useDante<PERSON>pi from "@/hooks/useDante<PERSON>pi";
import DModal from "../Global/DModal";
import * as plansService from '@/services/plans.service';
import DSelect from "../Global/DSelect";
import { useUserStore } from "@/stores/user/userStore";
import DButton from "../Global/DButton";
import { useNavigate } from "react-router-dom";

const DAddCreditsModal = ({ open, onClose }) => {
    const { user } = useUserStore((state) => state);
    const { data: addOns } = useDanteApi(plansService.getAddOns);
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    
    const [selectedPlan, setSelectedPlan] = useState({
        value: 10000,
        label: "Small Pack - Up to 10,000",
        price: 75,
        key: "extra_messages_small_pack",
        id: "6c1820de-2b53-46b6-a4bd-32bb8e66ce38",
        credits: 2500
    });

    const handleAddCredits = async () => {
        setIsLoading(true);
        try{
            const response = await plansService.getCheckoutSessionExtraMessages(selectedPlan?.id);
            if(response.status === 200){
                window.open(response.data.checkout_session.url, '_blank');
            }
        }catch(error){
            console.log(error);
        }finally{
            setIsLoading(false);
        }
    }

    console.log('user', user)
    console.log('addOns', addOns)
    return <DModal isOpen={open} onClose={onClose} title="Add Credits" className="md:min-w-[500px]"
        footer={<DButton variant="dark" fullWidth onClick={handleAddCredits} loading={isLoading}>
            Add Credits
        </DButton>}
    >
        <div className="flex flex-col gap-size2">
            <div className="flex items-center justify-between p-size2 bg-grey-5 rounded-size2">
                <p className="text-base tracking-tight">Your available credits</p>
                <p className="text-base tracking-tight">{Math.round(user?.credits_key?.credits_available - user?.credits_key.credits_used).toLocaleString('en-US')}</p>
            </div>
            <div className="flex flex-col gap-size1">
                <p className="text-sm tracking-tight font-medium">Choose your credit package</p>
                <DSelect
                    value={selectedPlan?.value}
                    options={addOns?.results?.filter((item) => item.key === 'extra_messages')[0]?.options}
                    onChange={(value) => {
                        setSelectedPlan({
                            value: value,
                            id: addOns?.results?.filter((item) => item.key === 'extra_messages')[0]?.options?.find((item) => item.value === value)?.id,
                            credits: addOns?.results?.filter((item) => item.key === 'extra_messages')[0]?.options?.find((item) => item.value === value)?.credits,
                            price: addOns?.results?.filter((item) => item.key === 'extra_messages')[0]?.options?.find((item) => item.value === value)?.price,
                        });
                    }}
                />
            </div>
            <div className="flex flex-col gap-size1">
                <p className="text-sm tracking-tight font-medium">Credits for chosen package</p>
                <p className="text-base tracking-tight font-medium">{selectedPlan?.credits}</p>
            </div>
            <div className="flex flex-col gap-size1">
                <p className="text-sm tracking-tight font-medium">Total charged amount</p>
                <p className="text-base tracking-tight font-medium">${selectedPlan?.price}</p>
            </div>
            <div className="flex flex-col gap-size1">
                <p className="text-sm tracking-tight font-medium">Total credits after purchase</p>
                <p className="text-base tracking-tight font-medium">{Math.round(selectedPlan?.credits + (user?.credits_key?.credits_available - user?.credits_key.credits_used)).toLocaleString('en-US')}</p>
            </div>

        </div>
    </DModal>;
};

export default DAddCreditsModal;
