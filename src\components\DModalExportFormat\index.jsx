import DModal from "../Global/DModal";
import DSelect from "../Global/DSelect";
import DButton from "../Global/DButton";
import { useState } from "react";

const DModalExportFormat = ({ open, onClose, onExport, loading }) => {
  const [format, setFormat] = useState('csv');

  const handleExport = () => {
    onExport(format);
  };

  return (
    <DModal
      isOpen={open}
      onClose={onClose}
      title="Export"
      footer={
        <div className="flex justify-end w-full">
          <DButton variant="dark" fullWidth onClick={handleExport} loading={loading}>
            Export
          </DButton>
        </div>
      }
    >
      <div className="flex flex-col gap-size1">
        <p className="text-sm font-medium tracking-tight">Select format</p>
        <DSelect
          options={[
            { label: 'CSV', value: 'csv' },
            { label: 'TXT', value: 'plain' }
          ]}
          value={format}
          onChange={(value) => setFormat(value)}
        />
      </div>
    </DModal>
  );
};

export default DModalExportFormat;