import React, { useState, useEffect } from "react";
import DModal from "../Global/DModal";
import ProfileForm from "../Profile/ProfileForm";
import DButton from "../Global/DButton";
import { useUserStore } from '@/stores/user/userStore';
import useToast from '@/hooks/useToast';
import useLogout from '@/application/auth/logout';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import * as userService from '@/services/user.service';
import * as teamManagementService from '@/services/teamManagement.service';
import validateEmail from '@/helpers/validateEmail';
import DConfirmationModal from '@/components/Global/DConfirmationModal';

const DModalProfileForm = ({ open, onClose, user, defaultImages, defaultTeamImages }) => {
  const { setUser } = useUserStore((state) => state);
  const { addSuccessToast } = useToast();
  const logout = useLogout();
  const { selectedTeam, setTeams, setSelectedTeam } = useTeamManagementStore(
    (state) => state
  );

  const [userData, setUserData] = useState({});
  const [profileImageFile, setAccountImage] = useState(null);
  const [profileImageUrl, setAccountImageUrl] = useState(
    user?.profile_image ?? defaultImages?.results[0]?.big_image_url
  );
  const [loading, setLoading] = useState(false);
  const [teamProfileImage, setAccountTeamImage] = useState();
  const [teamProfileImageUrl, setAccountTeamImageUrl] = useState(
    user?.team_key?.team_icon ?? defaultTeamImages?.results[0]?.big_image_url
  );
  const [isEmailChanged, setIsEmailChanged] = useState(false);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [showEnable2FA, setShowEnable2FA] = useState(false);
  const [showDisable2FA, setShowDisable2FA] = useState(false);
  const [modifiedData, setModifiedData] = useState({});
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const errors = {};
    if (!userData?.full_name) {
      errors.full_name = 'Name is required';
    }
    if (!userData?.email) {
      errors.email = 'Email is required';
    }
    if (!validateEmail(userData?.email)) {
      errors.email = 'Email is invalid';
    }

    if (!modifiedData?.profile_image && !profileImageFile && !profileImageUrl) {
      errors.profile_image = 'Account image is required';
    }

    if (
      !modifiedData?.team_profile_image &&
      !teamProfileImage &&
      !teamProfileImageUrl
    ) {
      errors.team_profile_image = 'Team icon is required';
    }
    setErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    setIsEmailChanged(false);
    if (!validateForm()) {
      return;
    }
    try {
      setLoading(true);
      const formData = new FormData();
      if (profileImageFile instanceof File) {
        formData.append('file', profileImageFile);
        const response = await userService.uploadFile(formData);
        if (response.status === 200) {
          modifiedData.profile_image = response?.data?.url;
        }
        formData.delete('file');
      }
      if (teamProfileImage instanceof File) {
        formData.append('file', teamProfileImage);
        const response = await userService.uploadFile(formData);
        if (response.status === 200) {
          modifiedData.team_profile_image = response?.data?.url;
        }
        formData.delete('file');
      }
      formData.append('team_profile_image', teamProfileImage);
      const response = await userService.updateUserProfile({
        ...modifiedData,
      });

      if (response.status === 200) {
        if (user.email !== userData.email) {
          logout();
        }

        //
        const responseTeam = await teamManagementService.getTeams();

        if (responseTeam.status === 200) {
          setTeams(responseTeam.data.results);
          if (selectedTeam) {
            const teamFound = responseTeam.data.results.find(
              (team) => team.id === selectedTeam.id
            );
            setSelectedTeam({ ...selectedTeam, ...teamFound });
          }
        }

        setUser({
          ...user,
          ...response.data,
          team_key: {
            ...user?.team_key,
            team_name: response?.data?.team_name,
            team_icon: response?.data?.team_profile_image,
          },
        });
        addSuccessToast({
          message: 'Profile updated successfully',
        });
      }
      onClose();
    } catch (error) {
      console.log('Failed to update profile', error);
    } finally {
      setLoading(false);
    }
  };

  // Props to pass to the ProfileForm component
  const profileFormProps = {
    user,
    onClose,
    defaultImages,
    defaultTeamImages,
    userData,
    setUserData,
    profileImageFile,
    setAccountImage,
    profileImageUrl,
    setAccountImageUrl,
    teamProfileImage,
    setAccountTeamImage,
    teamProfileImageUrl,
    setAccountTeamImageUrl, 
    showChangePassword,
    setShowChangePassword,
    showEnable2FA,
    setShowEnable2FA,
    showDisable2FA,
    setShowDisable2FA,
    modifiedData,
    setModifiedData,
    errors,
    setErrors
  };

  return (
    <>
      <DModal 
        isOpen={open} 
        onClose={onClose} 
        title="Account details" 
        className="!min-w-[600px]"
        footer={
          <footer className="flex w-full gap-size1 sticky bottom-0 bg-white py-size3 border-t border-grey-5 z-10">
            <DButton fullWidth size="sm" variant="grey" onClick={onClose}>
              Cancel
            </DButton>
            <DButton
              fullWidth
              size="sm"
              variant="dark"
              loading={loading}
              onClick={() => {
                if (user?.email !== userData?.email && !isEmailChanged) {
                  setIsEmailChanged(true);
                } else {
                  handleSubmit();
                }
              }}
            >
              Save changes
            </DButton>
          </footer>
        }
      >
        <ProfileForm {...profileFormProps} />
      </DModal>
      
      <DConfirmationModal
        open={isEmailChanged}
        onClose={() => setIsEmailChanged(false)}
        onConfirm={handleSubmit}
        title="Email changed"
        description="You're about to change your email address. This action will log you out, and you'll need to confirm the new email address. Are you sure you want to proceed?"
        confirmText="Confirm"
        cancelText="Cancel"
        variantConfirm="dark"
      />
    </>
  );
};

export default DModalProfileForm;