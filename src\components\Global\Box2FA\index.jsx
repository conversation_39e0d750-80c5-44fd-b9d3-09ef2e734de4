import React, { useState, useRef } from "react";
import "./index.css";

const Box2FA = ({ value, onChange, error }) => {
  const [code, setCode] = useState(value);
  const inputRefs = useRef([]);

  const handleChange = (value, index) => {
    if (/^[0-9]$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);
      onChange(newCode.join(""));
      // Move focus to the next input box after entering a valid number
      if (index < 5) {
        inputRefs.current[index + 1].focus();
      }
    }
  };

  const handleBackspace = (index) => {
    const newCode = [...code];
    if (newCode[index] === "") {
      // Move to the previous input box if the current one is empty
      if (index > 0) {
        inputRefs.current[index - 1].focus();
      }
    } else {
      // Clear the current box
      newCode[index] = "";
      setCode(newCode);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace") {
      handleBackspace(index);
    }
  };

  const handleFocus = (index) => {
    // When focusing on a box with a pre-existing value, set the cursor but don't clear it
    inputRefs.current[index].select();
  };

  return (
    <div className="flex flex-col gap-size0">
      <div className="flex space-x-2">
        {code.map((digit, index) => (
          <div key={index} className="relative w-full h-24">
            <input
              ref={(el) => (inputRefs.current[index] = el)}
              type="text"
              maxLength="1"
              value={digit}
              onChange={(e) => handleChange(e.target.value, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              onFocus={() => handleFocus(index)}
              className="w-full h-full text-center border border-gray-300 dark:border-white/30 rounded-md focus:outline-none focus:ring-2 focus:ring-grey-20 text-min-safe-input bg-white dark:bg-[#09081f] text-black dark:text-white caret-black dark:caret-white box-2fa-input"
            />
            {/* Custom cursor for dark mode */}
            {!digit && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none hidden dark:flex">
                <div className="h-8 w-1 bg-white animate-pulse"></div>
              </div>
            )}
          </div>
        ))}
      </div>
      {error && (
        <p className="font-medium tracking-tight text-error">{error}</p>
      )}
    </div>
  );
};

export default Box2FA;
