import clsx from 'clsx';

import { STATUS } from '@/constants';

import NoZapIcon from '../Icons/NoZapIcon';
import ZapIcon from '../Icons/ZapIcon';

const DBadge = ({ type, hiddenLabel, label, showIcon = true, size = 'sm', className, ...props }) => {
  let badgeStyle = '';
  let badgeSize = 'text-xs';
  if (size === 'sm') {
    badgeSize = 'text-xs';
  } else if (size === 'md') {
    badgeSize = 'text-base';
  }

  if (type === STATUS.ACTIVE || type === STATUS.SUCCESS || type === STATUS.VERIFIED) {
    badgeStyle = 'bg-green-5 text-green-300';
  } else if (type === STATUS.PAUSED || type === STATUS.WORKING || type === STATUS.UNVERIFIED) {
    badgeStyle = 'bg-orange-5 text-orange-300';
  } else if (type === STATUS.FAILURE ) {
    badgeStyle = 'bg-negative-5 text-negative-100';
  } else if (type === STATUS.AI) {
    badgeStyle = 'bg-purple-300 text-white';
  } else if (type === STATUS.TAKEN) {
    badgeStyle = 'bg-purple-5 text-purple-300';
  } else if (type === STATUS.CLOSED) {
    badgeStyle = 'bg-green-5 text-green-300';
  } else if (type === STATUS.DROPPED) {
    badgeStyle = 'bg-negative-5 text-negative-200';
  } else if (type === STATUS.RESOLVED) {
    badgeStyle = 'bg-green-5 text-green-300';
  } else {
    badgeStyle = 'bg-grey-5 text-black';
  }
  return (
    <div
      className={clsx(
        badgeStyle,
        badgeSize,
        !hiddenLabel ? 'w-auto py-size0 px-size1' : 'size-6',
        `rounded-size0 flex justify-center items-center gap-size1`,
        className
      )}
      {...props}
    >
      {type === STATUS.ACTIVE && showIcon ? (
        <ZapIcon className="text-green-300" width="16" height="16" />
      ) : type === STATUS.PAUSED && showIcon ? (
        <NoZapIcon className="text-orange-300" width="16" height="16" />
      ) : (
        ''
      )}
      <span className={hiddenLabel ? 'sr-only' : ''}>{label}</span>
    </div>
  );
};

export default DBadge;
