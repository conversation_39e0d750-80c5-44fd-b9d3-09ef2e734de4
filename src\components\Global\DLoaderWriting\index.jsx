import Lottie from 'lottie-react';

import animationData from './DLoaderWritingLottie.json';

const DLoaderWriting = () => {
  return (
    <>
      <style>{`.d-loader-write path {fill: var(--dt-color-brand-100);}`}</style>
      <Lottie
        isClickToPauseDisabled
        ariaRole=""
        animationData={animationData}
        loop={true}
        autoplay={true}
        style={{ height: 16, width: 18, margin: 0 }}
        eventListeners={[]}
      ></Lottie>
    </>
  );
};

export default DLoaderWriting;
