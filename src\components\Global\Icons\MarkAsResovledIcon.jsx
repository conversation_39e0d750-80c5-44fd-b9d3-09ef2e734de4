// src/components/Global/Icons/MarkAsResovledIcon.jsx

import * as React from 'react';

const MarkAsResovledIcon = (props) => (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.161 0.237041C8.25629 0.158004 8.3762 0.114746 8.5 0.114746C8.6238 0.114746 8.74371 0.158004 8.839 0.237041C10.8366 1.8987 13.3215 2.86379 15.917 2.98604C16.034 2.98964 16.1459 3.03414 16.2335 3.1118C16.321 3.18945 16.3785 3.29535 16.396 3.41104C16.465 3.93104 16.5 4.46104 16.5 5.00104C16.5 10.163 13.24 14.564 8.666 16.257C8.55886 16.2965 8.44114 16.2965 8.334 16.257C3.76 14.564 0.5 10.163 0.5 5.00004C0.5 4.46204 0.535 3.93104 0.604 3.41104C0.621528 3.29518 0.67919 3.18916 0.766919 3.11148C0.854649 3.03381 0.966873 2.98941 1.084 2.98604C3.67927 2.8633 6.16379 1.89886 8.161 0.237041ZM12.357 6.19104C12.474 6.03005 12.5222 5.8292 12.491 5.63266C12.4599 5.43612 12.352 5.26 12.191 5.14304C12.03 5.02608 11.8292 4.97786 11.6326 5.00899C11.4361 5.04012 11.26 5.14805 11.143 5.30904L7.66 10.099L5.78 8.21904C5.71078 8.14744 5.628 8.09034 5.53647 8.05108C5.44495 8.01182 5.34653 7.99118 5.24694 7.99036C5.14736 7.98954 5.04861 8.00856 4.95646 8.04632C4.8643 8.08407 4.78059 8.1398 4.7102 8.21025C4.63982 8.28071 4.58417 8.36447 4.5465 8.45666C4.50883 8.54885 4.4899 8.64762 4.49081 8.7472C4.49173 8.84678 4.51246 8.94519 4.55181 9.03667C4.59116 9.12816 4.64834 9.21089 4.72 9.28004L7.22 11.78C7.29663 11.8567 7.38896 11.9159 7.49065 11.9534C7.59233 11.991 7.70094 12.006 7.80901 11.9976C7.91708 11.9891 8.02203 11.9573 8.11663 11.9044C8.21123 11.8515 8.29324 11.7787 8.357 11.691L12.357 6.19104Z" fill="#33C067"/>
    </svg>
);

export default MarkAsResovledIcon;
