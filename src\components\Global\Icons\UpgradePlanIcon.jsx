import * as React from "react"
const UpgradePlanIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    data-name="Flat Gradient Flaticon"
    viewBox="0 0 64 64"
    width={20}
    height={20}
    {...props}
  >
    <linearGradient
      id="a"
      x1={4}
      x2={60}
      y1={32}
      y2={32}
      gradientUnits="userSpaceOnUse"
    >
      <stop offset={0} stopColor="#9cffac" />
      <stop offset={1} stopColor="#00b59c" />
    </linearGradient>
    <path
      fill="url(#a)"
      d="M43.462 26.338 37.88 16.104h4.937l11.58 10.234zM21.056 16.104H16.12L4.551 26.338h10.933zm19.99 10.234-5.582-10.234H23.472l-5.573 10.234zm-23.38 2.119 11.728 29.482 11.865-29.482zM4 28.457 25.993 55.1 15.388 28.457zm39.547 0L32.953 54.76l21.972-26.304zM7.517 11.1H9.38v1.863a1.06 1.06 0 0 0 2.12 0V11.1h1.861a1.06 1.06 0 0 0 0-2.119H11.5V7.12a1.06 1.06 0 1 0-2.119 0v1.862H7.518a1.06 1.06 0 0 0 0 2.119zm51.444 27.834h-1.862v-1.862a1.06 1.06 0 0 0-2.119 0v1.862h-1.863a1.06 1.06 0 1 0 0 2.119h1.863v1.862a1.06 1.06 0 1 0 2.119 0v-1.862h1.862a1.06 1.06 0 0 0 0-2.119z"
    />
  </svg>
)
export default UpgradePlanIcon
