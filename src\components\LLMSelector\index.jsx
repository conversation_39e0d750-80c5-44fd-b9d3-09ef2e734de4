import { useEffect, useState } from 'react';
import { groupBy } from 'lodash';

import { iconToCompanyName, LLM_MODEL_DEFAULT } from '@/constants';
import loadIcon from '@/helpers/loadIcons';
import useDanteApi from '@/hooks/useDanteApi';
import { getLLMModels } from '@/services/model.service';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import { updateChatbotLLMModel } from '@/services/chatbot.service';
import ChevronDownIcon from '../Global/Icons/ChevronDownIcon';

import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import OpenAiLogoIcon from '../Global/Icons/OpenAiLogoIcon';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { useParams } from 'react-router-dom';
import DTooltip from '../Global/DTooltip';
import featureCheck from '@/helpers/tier/featureCheck';

const LLMSelector = () => {
  const { user } = useUserStore();
  const params = useParams();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const [selectedLLM, setSelectedLLM] = useState(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  const setSelectedLLModel = useChatbotStore(
    (state) => state.setSelectedLLModel
  );
  const [icons, setIcons] = useState({});

  const [modelsByCompany, setModelsByCompany] = useState({});

  const { data: models } = useDanteApi(getLLMModels, [], {}, {
    tier_type: user.tier_type,
  });

  const { addWarningToast } = useToast();

  const handleChangeModel = async (model) => {
    if(model.value === 'cohere' && !featureCheck('cohere_model')) {
      return;
    }

    if(model.value.includes('claude') && !featureCheck('claude_model')) {
      return;
    }

    if (!model.available) {
      addWarningToast({
        message:
          'This model is not available for your plan. Please upgrade your plan to use this model.',
      });
      return;
    }
    setSelectedLLM({ ...model, Icon: icons[model.Logo] });
    setSelectedLLModel(model);

    try{
        await updateChatbotLLMModel({
            kb_id: params?.id,
            llmModel: model.value
        });
    } catch (e) {
        console.error('Failed to update chatbot LLM model', e);
    }
  };

  useEffect(() => {
    const loadModels = async () => {
      const groupedModels = groupBy(models, 'Logo');

      const iconComponents = {};

      for (const company of Object.keys(groupedModels)) {
        try {
          const iconCompany = await loadIcon(company);
          iconComponents[company] = iconCompany;
        } catch (e) {
          console.error(`Failed to load icon ${company}`, e);
        }
      }

      setIcons(iconComponents);
      setModelsByCompany(groupedModels);
    };
    if (models?.length) {
      loadModels();
    }
  }, [models]);

  useEffect(() => {
    setSelectedLLM(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  }, [selectedChatbot?.last_model_used]);

  return (
    <Listbox
      value={selectedLLM}
      onChange={handleChangeModel}
      className="group"
      as="div"
    >
      <ListboxButton className="flex items-center justify-between gap-size1 text-xs ">
        <div className="flex items-center justify-center h-full">
          {selectedLLM.Icon ? (
            <selectedLLM.Icon className="size-4" />
          ) : (
            <OpenAiLogoIcon className="size-4" />
          )}
        </div>
        <div className="flex items-center justify-center">
          {selectedLLM.label}
        </div>
        <ChevronDownIcon
          className="transition-transform group pointer-events-none size-3 fill-black group-data-[open]:rotate-180"
          aria-hidden="true"
        />
      </ListboxButton>
      <ListboxOptions
        anchor={{
          to: 'top start',
          gap: 8,
        }}
        transition
        className="flex flex-col bg-white gap-size2 py-size2 rounded-size2 border border-grey-10 dark:border-grey-20 origin-bottom transition duration-200 ease-out data-[closed]:scale-95 data-[closed]:opacity-0"
      >
        {Object.keys(modelsByCompany)?.map((company) => {
          const Icon = icons[company];

          return (
            <div key={company} className="flex flex-col gap-size0">
              <span className="text-xs font-medium text-grey-50 px-size2">
                {iconToCompanyName[company]}
              </span>
              {modelsByCompany[company].map((model) => (
                <ListboxOption
                  as="button"
                  key={model.value}
                  value={model}
                  className={`flex items-center gap-size1 text-sm hover:bg-grey-5 py-size0 px-size2 cursor-pointer data-[selected]:text-grey-50 data-[disabled]:opacity-50 data-[disabled]:cursor-default data-[disabled]:hover:bg-white`}
                  disabled={!model.available}
                >
                    {Icon && <Icon className="size-4" />}
                    <div className='flex flex-col items-start'>
                      <span>{model.label}</span>
                      <span className='text-xs text-grey-50 leading-none'>{model.credits} credits per response</span>
                    </div>
                </ListboxOption>
              ))}
            </div>
          );
        })}
      </ListboxOptions>
    </Listbox>
  );
};

export default LLMSelector;
