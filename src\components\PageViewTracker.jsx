import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * PageViewTracker component
 * Tracks page views by sending an event to the dataLayer whenever the URL changes
 */
function PageViewTracker() {
  const location = useLocation();
  const prevPathRef = useRef("");

  useEffect(() => {
    // Get the current URL and path
    const pageUrl = window.location.origin + location.pathname + location.search;
    const pagePath = location.pathname + location.search;

    // Use previous path as referrer, or document.referrer for first page
    const pageReferrer = prevPathRef.current
      ? (window.location.origin + prevPathRef.current)
      : document.referrer;

    // Create timestamp for the event
    const timestamp = new Date().toISOString();

    // Push to dataLayer
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "page_viewed",
      page_url: pageUrl,
      page_path: pagePath,
      page_referrer: pageReferrer,
      timestamp: timestamp
    });

    // Update the previous path ref
    prevPathRef.current = location.pathname + location.search;
  }, [location.pathname, location.search]);

  return null;
}

export default PageViewTracker;
