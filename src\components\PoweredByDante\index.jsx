import React, { useEffect, useState } from 'react';

import getBrightnessColor from '@/helpers/getBrightnessColor';
import DFullLogo from '../Global/DLogo/DFullLogo';
import useLayoutStore from '@/stores/layout/layoutStore';

const PoweredByDante = ({ element, surface, variant = 'color', isPreviewMode = false }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const isDarkModeLayout = useLayoutStore((state) => state.isDarkMode);

  useEffect(() => {
    const isDark =
      isDarkModeLayout || (surface && getBrightnessColor(surface) < 220);

    if (isDark) {
      setIsDarkMode(true);
    } else {
      setIsDarkMode(false);
    }

    return () => {};
  }, [isDarkModeLayout, surface]);

  //TODO: redirect to https://dante-ai.com/

  return (
    <a
      href="https://dante-ai.com/"
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center text-xs gap-size0"
      style={{ color: `var(--dt-color-element-20)` }}
    >
      Powered by <span className="sr-only">Dante</span>
      <DFullLogo
        mode={isDarkMode && !isPreviewMode ? 'dark' : 'light'}
        variant={variant}
        size="xs"
      />
    </a>
  );
};

export default PoweredByDante;
