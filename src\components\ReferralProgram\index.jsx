import React, { useState, useMemo } from 'react';
import DButton from '@/components/Global/DButton';
import DModal from '@/components/Global/DModal';
import GiftIcon from '@/components/Global/Icons/GiftIcon';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import { useUserStore } from '@/stores/user/userStore';

const ReferralProgram = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [copied, setCopied] = useState(false);
  const user = useUserStore(state => state.user);
  // Mock data - in a real implementation, this would come from an API
  const referralLink = import.meta.env.VITE_APP_BASE_URL + 'sign-up?referred_by=' + user?.id;

  const handleCopyLink = () => {
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const openModal = (tabIndex = 0) => {
    setActiveTab(tabIndex);
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  // Memoize the button to prevent unnecessary re-renders
  const referralButton = useMemo(() => (
    <DButton
      onClick={() => openModal(1)}
      className="flex items-center gap-size1"
      size="sm"
    >
      <GiftIcon className="w-4 h-4 !text-grey-75" />
      <span className='text-grey-75'>Earn $50</span>
    </DButton>
  ), []);

  return (
    <>
      {referralButton}

      <DModal
        isOpen={isOpen}
        onClose={closeModal}
        title="Earn $50"
      >
        <div className="mb-size4">
          <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
            <Tab.List className="flex space-x-1 border-b border-grey-5">
              <Tab
                className={({ selected }) =>
                  clsx(
                    'py-size0 px-size4 text-sm font-medium focus:outline-none',
                    selected
                      ? 'text-purple-300 border-b-2 border-purple-300'
                      : 'text-grey-50 hover:text-grey-300'
                  )
                }
              >
                Invite
              </Tab>
              <Tab
                className={({ selected }) =>
                  clsx(
                    'py-size0 px-size4 text-sm font-medium focus:outline-none',
                    selected
                      ? 'text-purple-300 border-b-2 border-purple-300'
                      : 'text-grey-50 hover:text-grey-300'
                  )
                }
              >
                Rewards
              </Tab>
            </Tab.List>

            <Tab.Panels className="mt-size4">
              {/* Invite Tab Content */}
              <Tab.Panel className="space-y-size4">
                <div className="space-y-size2">
                  <h2 className="text-lg font-medium">Invite your friends and get paid!</h2>
                  <p className="text-xs text-grey-50 leading-none">
                    Your friends get 50% off the first 3 months, and you'll earn 40%
                    of any purchase they make (up to $50) as a reward. The more
                    friends you refer, the more you earn!
                  </p>
                </div>

                <div className="p-size4 bg-grey-2 rounded-size1 space-y-size2">
                  <p className="text-sm text-grey-50">Invite link</p>
                  <div className="flex flex-col items-center gap-size2">
                    <div className="flex items-center gap-size2 bg-white p-size2 rounded-size1 flex-grow w-full">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <GiftIcon className="w-5 h-5 text-purple-300" />
                      </div>
                      <span className="text-grey-300 truncate">{referralLink}</span>
                    </div>
                    <DButton
                      onClick={handleCopyLink}
                      variant="purple"
                      size="sm"
                      className="whitespace-nowrap !bg-purple-200 text-white rounded-sm"
                      fullWidth
                    >
                      {copied ? (
                        <CheckmarkIcon className="w-4 h-4 mr-size1" />
                      ) : (
                        <CopyIcon className="w-4 h-4 mr-size1" />
                      )}
                      {copied ? "Copied" : "Copy invite link"}
                    </DButton>
                  </div>
                </div>

                {/* <div className="flex justify-center space-x-size3">
                  <button
                    onClick={() => handleShare('twitter')}
                    className="w-10 h-10 rounded-full border border-grey-5 flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleShare('email')}
                    className="w-10 h-10 rounded-full border border-grey-5 flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
                      <polyline points="22,6 12,13 2,6" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleShare('linkedin')}
                    className="w-10 h-10 rounded-full border border-grey-5 flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                      <rect x="2" y="9" width="4" height="12" />
                      <circle cx="4" cy="4" r="2" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleShare('copy')}
                    className="w-10 h-10 rounded-full border border-grey-5 flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
                    </svg>
                  </button>
                </div> */}
              </Tab.Panel>

              {/* Rewards Tab Content - Updated to match the screenshot */}
              <Tab.Panel className="space-y-size4">
                <div className="space-y-size1">
                  <h2 className="text-lg font-medium">Your Rewards</h2>
                  <p className="text-xs text-grey-50 leading-none">
                    Earn 40% of any purchase your friends make (up to $50) as a reward. Track your referrals and rewards here.
                  </p>
                </div>

                <div className="border border-grey-5 rounded-size1 p-size5 bg-grey-2">
                  <div className="flex flex-col">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-grey-50">Total rewards earned</p>
                        <p className="text-2xl font-medium">$0.00</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-grey-50">Successful referrals</p>
                        <p className="text-2xl font-medium">0</p>
                      </div>
                    </div>

                    <div className="w-full h-px bg-grey-5 my-size4"></div>

                    <div className="flex justify-between items-center">
                      <p className="text-lg">Pending rewards</p>
                      <p className="text-lg font-medium">$0.00</p>
                    </div>
                  </div>
                </div>

                <div className="text-center p-size3 bg-grey-2 rounded-size1">
                  <p className="text-base mb-size4">
                    Start inviting friends to earn rewards!
                  </p>
                  <DButton
                    onClick={() => setActiveTab(0)}
                    size="sm"
                    variant='dark'
                    fullWidth
                  >
                    Get invite link
                  </DButton>
                </div>
              </Tab.Panel>
            </Tab.Panels>
          </Tab.Group>
        </div>
      </DModal>
    </>
  );
};

export default ReferralProgram;
