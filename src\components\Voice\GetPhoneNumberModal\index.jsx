import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DModal from '@/components/Global/DModal';
import DButton from '@/components/Global/DButton';
import SuccessIcon from '@/components/Global/Icons/SuccessIcon';
import { useUserStore } from "@/stores/user/userStore";
import useModalStore from '@/stores/modal/modalStore';
import PhoneIcon from '@/components/Global/Icons/PhoneIcon';

const GetPhoneNumberModal = ({ isOpen, onClose, id }) => {
  const navigate = useNavigate();
  const { user } = useUserStore();
  const openPlansModal = useModalStore((state) => state.openPlansModal);
  const [shouldShowModal, setShouldShowModal] = useState(false);

  useEffect(() => {
    // Check if the user has seen the popup before
    const hasSeenPopup = localStorage.getItem('hasSeenPhoneNumberPopup');
    
    if (!hasSeenPopup && isOpen) {
      setShouldShowModal(true);
      // Mark that the user has seen the popup
      localStorage.setItem('hasSeenPhoneNumberPopup', 'true');
    } else {
      setShouldShowModal(false);
    }
  }, [isOpen]);

  const handleClose = () => {
    setShouldShowModal(false);
    onClose();
  };
  
  return (
    <DModal
      isOpen={shouldShowModal}
      onClose={handleClose}
      // title="Connect your AI voice agent to a phone number"
      className="!max-w-[500px]"
    >
      <div className="flex flex-col items-center gap-6 py-8 pb-0">
        <div className="w-20 h-20 bg-green-50 rounded-full flex items-center justify-center">
          <PhoneIcon className='text-green-500 size-6' />
        </div>
        
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-semibold text-gray-900">
            Connect your AI voice agent to a phone number
          </h3>
          
          <p className="text-gray-600 leading-relaxed text-sm">
          Connect your AI assistant to a dedicated phone number and start handling calls with ease.
          </p>

          {user.tier_type === 'free' ? (
            <>
              <DButton 
                fullWidth 
                variant="green" 
                size="sm"
                onClick={() => openPlansModal('nr_free_phone_numbers')}
                className="text-lg font-medium"
              >
                Connect your AI voice agent to the real world
              </DButton>
              
              <p className="text-gray-600 leading-relaxed text-sm">
                To connect your AI agent to a phone number, you'll need to upgrade your plan first. 
                Starter plans and above include a free phone number. Upgrade now to start receiving calls to your AI agent!
              </p>
            </>
          ) : (
            <>
              <DButton 
                fullWidth 
                variant="green" 
                size="sm"
                onClick={() => navigate('/phone-numbers/purchase')}
                className="text-lg font-medium"
              >
                <span className='text-sm'>Claim your free business number</span>
              </DButton>
              
              <p className="text-gray-600 leading-relaxed text-sm">
                If you'd like to call your AI agent from an external phone number, you'll need to connect a phone number first. <br/> <br/> Don't worry, you can always come back later to get your free number, or if you already have a phone number you'd like to use, you can add it on the <a href="/phone-numbers" className="text-gray-500 hover:text-blue-800 underline">Phone Numbers page</a>.
              </p>
            </>
          )}
        </div>
      </div>
    </DModal>
  );
};

export default GetPhoneNumberModal;
