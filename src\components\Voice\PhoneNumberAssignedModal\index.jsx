import { useNavigate } from 'react-router-dom';
import DModal from '@/components/Global/DModal';
import DButton from '@/components/Global/DButton';
import PhoneIcon from '@/components/Global/Icons/PhoneIcon';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';

const PhoneNumberAssignedModal = ({ isOpen, onClose, voiceId, phoneNumber }) => {
  const navigate = useNavigate();
  
  const handleViewConversations = () => {
    navigate(`/voice/${voiceId}/conversations?source=onboarding-popup`);
  };
  
  return (
    <DModal
      isOpen={isOpen}
      onClose={onClose}
      // title="Phone Number Connected"
      footer={
        <div className='flex flex-col gap-2 w-full'>
        <DButton 
          fullWidth 
          variant="dark" 
          onClick={handleViewConversations}
        >
          View live conversations
        </DButton>
        {/* <DButton 
          fullWidth 
          variant="outlined" 
          onClick={() => navigate(`/voice/${voiceId}`)}
        >
          Go to AI voice agent
          </DButton> */}
        </div>
      }
    >
      <div className="flex flex-col items-center gap-size4 py-size2">
        <div className="flex items-center gap-4">
          {/* <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center"> */}
            <DFullLogo className="w-8 h-8" />
          {/* </div> */}
          
          <div>
            <ChevronRightIcon className="w-6 h-6 text-grey-50" />
          </div>

          <div className="w-14 h-14 bg-purple-10 rounded-full flex items-center justify-center">
            <PhoneIcon className="w-5 h-5 text-purple-300" />
          </div>
        </div>
        
        <p className="text-center text-grey-75">
          Call the phone number below to talk to your AI voice agent.
        </p>

        <div className='flex items-center justify-between w-full border border-grey-5 rounded-md p-2'>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300..700&display=swap');
        </style>
        <p style={{ fontFamily: "'Fira Code', monospace" }}>{phoneNumber} </p>
        <DButtonIcon onClick={() => navigator.clipboard.writeText(phoneNumber)}>
          <CopyIcon />
        </DButtonIcon>
        </div>

        <p className="text-center text-grey-75">
        View your AI voice agent's live conversations:
        </p>
      </div>
    </DModal>
  );
};

export default PhoneNumberAssignedModal;
