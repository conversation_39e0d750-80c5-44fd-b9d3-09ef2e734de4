/**
 * Updates the dataLayer with user information for analytics purposes
 *
 * @param {Object} user - User data object from the userStore
 * @param {Object} auth - Auth data object from the userStore
 */
export const updateUserDataLayer = (user, auth) => {
  if (typeof window === 'undefined') return;

  // Check if the user is actually logged in by checking for an access token
  const isLoggedIn = !!auth?.access_token;

  window.dataLayer = window.dataLayer || [];

  // Only send user details if the user is logged in
  if (isLoggedIn) {
    // Include full user details only when logged in
    window.dataLayer.push({
      'event': 'user_details',
      'user_id': user?.id || auth?.user_id || '',
      'email': user?.email || auth?.email || '',
      'login_status': 'true',
      'first_name': user?.full_name?.split(' ')[0] || '',
      'last_name': user?.full_name?.split(' ').slice(1).join(' ') || '',
      'tier_name': user?.tier_type || '',
      'billing_cycle': user?.billing_cycle || 'none',
      'prev_tier_name': user?.prev_tier_name || '',
      'prev_billing_cycle': user?.prev_billing_cycle || '',
      'billing_price': user?.tier_price_as_float || 0,
      'payment_method': user?.payment_method || 'Credit Card',
      'trial_signup_date': user?.trial_signup_date || ''
    });
  } else {
    // When logged out, only send minimal information
    window.dataLayer.push({
      'event': 'user_details',
      'login_status': 'false',
      'user_id': '',
      'email': ''
    });
  }
};

// Helper function to pad numbers with leading zeros
const padZero = (num) => {
  return num.toString().padStart(2, '0');
};

/**
 * Formats a date in MM/DD/YYYY HH:MM:SS format using UTC time
 *
 * @param {Date} date - The date to format
 * @returns {string} The formatted date string
 */
const formatDateWithTimeUTC = (date) => {
  return `${padZero(date.getUTCMonth() + 1)}/${padZero(date.getUTCDate())}/${date.getUTCFullYear()} ${padZero(date.getUTCHours())}:${padZero(date.getUTCMinutes())}:${padZero(date.getUTCSeconds())}`;
};

/**
 * Tracks subscription events in the dataLayer for Amplitude
 *
 * @param {Object} subscriptionData - Object containing subscription details
 * @param {string} subscriptionData.subscription_id - The subscription/order ID
 * @param {string} subscriptionData.user_id - User ID
 * @param {string} subscriptionData.email - User email
 * @param {string} subscriptionData.tier_name - Current tier name (e.g. "advanced", "pro", "enterprise")
 * @param {string} subscriptionData.billing_cycle - Current billing cycle (e.g. "monthly", "yearly")
 * @param {string} subscriptionData.prev_tier_name - Previous tier name if upgrading/downgrading
 * @param {string} subscriptionData.prev_billing_cycle - Previous billing cycle if changing
 * @param {number} subscriptionData.billing_price - The price charged for the subscription
 * @param {string} subscriptionData.next_payment_date - Formatted date of next payment (MM/DD/YYYY HH:MM:SS)
 * @param {string} subscriptionData.payment_method - Payment method (e.g. "credit card", "paypal")
 * @param {string} subscriptionData.subscription_date - Formatted date of subscription (MM/DD/YYYY HH:MM:SS)
 * @param {string} subscriptionData.last_subscription_date - Last subscription date if available
 * @param {string} subscriptionData.subscription_method - Method of subscription (e.g. "direct", "upgrade", "downgrade")
 */
export const trackSubscription = (subscriptionData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Clear the previous ecommerce object
  window.dataLayer.push({ ecommerce: null });

  // Push the subscription data
  window.dataLayer.push({
    event: "plan_subscribed",
    ecommerce: {
      subscription_id: subscriptionData.subscription_id || '',
      user_id: subscriptionData.user_id || '',
      email: subscriptionData.email || '',
      tier_name: subscriptionData.tier_name || '',
      billing_cycle: subscriptionData.billing_cycle || 'monthly',
      prev_tier_name: subscriptionData.prev_tier_name || '',
      prev_billing_cycle: subscriptionData.prev_billing_cycle || '',
      billing_price: subscriptionData.billing_price || 0,
      next_payment_date: subscriptionData.next_payment_date || '',
      payment_method: subscriptionData.payment_method || 'credit card',
      subscription_date: subscriptionData.subscription_date || '',
      last_subscription_date: subscriptionData.last_subscription_date || '',
      subscription_method: subscriptionData.subscription_method || 'direct'
    }
  });
};

/**
 * Tracks subscription cancellation events in the dataLayer for Amplitude
 *
 * @param {Object} cancellationData - Object containing cancellation details
 * @param {string} cancellationData.subscription_id - The subscription/order ID
 * @param {string} cancellationData.user_id - User ID
 * @param {string} cancellationData.email - User email
 * @param {string} cancellationData.tier_name - Cancelled tier name (e.g. "advanced", "pro", "enterprise")
 * @param {string} cancellationData.billing_cycle - Cancelled billing cycle (e.g. "monthly", "yearly")
 * @param {number} cancellationData.billing_price - The price of the cancelled subscription
 * @param {string} cancellationData.next_payment_date - Formatted date of next scheduled payment (MM/DD/YYYY HH:MM:SS)
 * @param {string} cancellationData.payment_method - Payment method (e.g. "credit card", "paypal")
 * @param {string} cancellationData.subscription_date - Formatted date when subscription started (MM/DD/YYYY HH:MM:SS)
 * @param {string} cancellationData.subscription_method - Method of subscription (e.g. "direct", "upgrade", "downgrade")
 * @param {string} cancellationData.subscription_cancellation_date - Formatted date of cancellation (MM/DD/YYYY HH:MM:SS)
 */
export const trackSubscriptionCancellation = (cancellationData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Clear the previous ecommerce object
  window.dataLayer.push({ ecommerce: null });

  // Push the cancellation data
  window.dataLayer.push({
    event: "subscription_cancelled",
    ecommerce: {
      subscription_id: cancellationData.subscription_id || '',
      user_id: cancellationData.user_id || '',
      email: cancellationData.email || '',
      tier_name: cancellationData.tier_name || '',
      billing_cycle: cancellationData.billing_cycle || '',
      billing_price: cancellationData.billing_price || 0,
      next_payment_date: cancellationData.next_payment_date || '',
      payment_method: cancellationData.payment_method || 'credit card',
      subscription_date: cancellationData.subscription_date || '',
      subscription_method: cancellationData.subscription_method || 'direct',
      subscription_cancellation_date: cancellationData.subscription_cancellation_date || '',
    }
  });
};

/**
 * Tracks page view events in the dataLayer for Amplitude
 * Should be called whenever the URL changes in the application
 *
 * @param {Object} location - The current location object (from React Router or window.location)
 * @param {string} referrer - The referrer URL (optional)
 */
export const trackPageView = (location, referrer = '') => {
  if (typeof window === 'undefined') return;

  // Get the current URL and path
  const pageUrl = window.location.origin + location.pathname + location.search;
  const pagePath = location.pathname + location.search;

  // Use provided referrer or document.referrer if available
  const pageReferrer = referrer || document.referrer || '';

  window.dataLayer = window.dataLayer || [];

  // Push the page view data
  window.dataLayer.push({
    event: "page_viewed",
    page_url: pageUrl,
    page_path: pagePath,
    page_referrer: pageReferrer,
    timestamp: new Date().toISOString()
  });
};

/**
 * Tracks user signup events in the dataLayer for Amplitude
 *
 * @param {Object} signupData - Object containing signup details
 * @param {string} signupData.user_id - User ID
 * @param {string} signupData.email - User email
 * @param {string} signupData.first_name - User's first name (if available)
 * @param {string} signupData.last_name - User's last name (if available)
 * @param {string} signupData.tier_name - Initial tier name (e.g. "starter", "advanced", "pro")
 * @param {string} signupData.billing_cycle - Initial billing cycle (e.g. "monthly", "yearly", "none")
 * @param {string} signupData.signup_method - Method of signup (e.g. "Email", "Google")
 */
export const trackSignup = (signupData) => {
  if (typeof window === 'undefined') return;

  // Format signup date in UTC
  const currentDate = new Date();
  const formattedSignupDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Push the signup data
  window.dataLayer.push({
    event: "sign_up",
    user_id: signupData.user_id || '',
    email: signupData.email || '',
    first_name: signupData.first_name || '',
    last_name: signupData.last_name || '',
    tier_name: signupData.tier_name || 'starter',
    billing_cycle: signupData.billing_cycle || 'none',
    signup_date: formattedSignupDate,
    signup_method: signupData.signup_method || 'Email'
  });
};

/**
 * Tracks user login events in the dataLayer for Amplitude
 *
 * @param {Object} loginData - Object containing login details
 * @param {string} loginData.email - User's email
 * @param {number} loginData.total_logins - Total number of times user has logged in
 * @param {string} loginData.last_login_date - Date of previous login if available (MM/DD/YYYY HH:MM:SS)
 */
export const trackLogin = (loginData) => {
  if (typeof window === 'undefined') return;

  // Format login date in UTC
  const currentDate = new Date();
  const formattedLoginDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Push the login data
  window.dataLayer.push({
    event: "log_in",
    email: loginData.email || '',
    login_date: formattedLoginDate,
    last_login_date: loginData.last_login_date || '',
    total_logins: loginData.total_logins || 1
  });
};

/**
 * Tracks first chatbot creation success events in the dataLayer
 *
 * @param {Object} chatbotData - Object containing chatbot creation details
 * @param {string} chatbotData.user_id - User ID
 * @param {string} chatbotData.email - User email
 * @param {string} chatbotData.chatbot_id - ID of the created chatbot
 * @param {string} chatbotData.chatbot_name - Name of the created chatbot
 * @param {string} chatbotData.chatbot_type - Type of the created chatbot
 * @param {string} chatbotData.tier_name - User's tier at time of creation
 */
export const trackFirstChatbotCreation = (chatbotData) => {
  if (typeof window === 'undefined') return;

  // Format creation date in UTC
  const currentDate = new Date();
  const formattedCreationDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Push the chatbot creation data
  window.dataLayer.push({
    event: "first_chatbot_creation_success",
    user_id: chatbotData.user_id || '',
    email: chatbotData.email || '',
    chatbot_id: chatbotData.chatbot_id || '',
    chatbot_name: chatbotData.chatbot_name || '',
    chatbot_type: chatbotData.chatbot_type || '',
    tier_name: chatbotData.tier_name || '',
    creation_date: formattedCreationDate
  });
};

/**
 * Tracks first login events in the dataLayer
 *
 * @param {Object} firstLoginData - Object containing first login details
 * @param {string} firstLoginData.user_id - User ID
 * @param {string} firstLoginData.email - User's email
 * @param {string} firstLoginData.signup_method - Method used for signup (e.g. "Email", "Google")
 */
export const trackFirstLogin = (firstLoginData) => {
  if (typeof window === 'undefined') return;

  // Format login date in UTC
  const currentDate = new Date();
  const formattedLoginDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Push the first login data
  window.dataLayer.push({
    event: "first_log_in",
    user_id: firstLoginData.user_id || '',
    email: firstLoginData.email || '',
    signup_method: firstLoginData.signup_method || 'Email',
    first_login_date: formattedLoginDate
  });
};

/**
 * Tracks trial signup events in the dataLayer
 *
 * @param {Object} trialData - Object containing trial signup details
 * @param {string} trialData.user_id - User ID
 * @param {string} trialData.email - User email
 * @param {string} trialData.tier_name - Trial tier name
 * @param {string} trialData.billing_cycle - Billing cycle after trial (e.g. "monthly", "yearly")
 * @param {number} trialData.trial_days - Number of days in the trial period
 * @param {string} trialData.payment_method - Payment method used (optional, for pre-authorized payment methods)
 */
export const trackTrialSignup = (trialData) => {
  if (typeof window === 'undefined') return;

  // Format trial signup date in UTC
  const currentDate = new Date();
  const formattedSignupDate = formatDateWithTimeUTC(currentDate);

  // Calculate trial end date
  const trialEndDate = new Date(currentDate);
  trialEndDate.setDate(trialEndDate.getDate() + (trialData.trial_days || 14));
  const formattedTrialEndDate = formatDateWithTimeUTC(trialEndDate);

  window.dataLayer = window.dataLayer || [];

  // Push the trial signup data
  window.dataLayer.push({
    event: "trial_sign_up",
    user_id: trialData.user_id || '',
    email: trialData.email || '',
    tier_name: trialData.tier_name || '',
    billing_cycle: trialData.billing_cycle || 'monthly',
    trial_signup_date: formattedSignupDate,
    trial_end_date: formattedTrialEndDate,
    trial_days: trialData.trial_days || 14,
    payment_method: trialData.payment_method || ''
  });
};

/**
 * Tracks chatbot training events in the dataLayer
 *
 * @param {Object} trainingData - Object containing training details
 * @param {string} trainingData.user_id - User ID
 * @param {string} trainingData.email - User email
 * @param {string} trainingData.chatbot_id - ID of the trained chatbot
 * @param {string} trainingData.chatbot_name - Name of the trained chatbot
 * @param {string} trainingData.training_method - Method used for training (e.g. "documents", "website", "api")
 * @param {number} trainingData.document_count - Number of documents used for training (if applicable)
 * @param {number} trainingData.token_count - Number of tokens processed (if available)
 * @param {string} trainingData.tier_name - User's tier at time of training
 */
export const trackChatbotTraining = (trainingData) => {
  if (typeof window === 'undefined') return;

  // Format training date in UTC
  const currentDate = new Date();
  const formattedTrainingDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Push the training data
  window.dataLayer.push({
    event: "train_chatbot",
    user_id: trainingData.user_id || '',
    email: trainingData.email || '',
    chatbot_id: trainingData.chatbot_id || '',
    chatbot_name: trainingData.chatbot_name || '',
    training_date: formattedTrainingDate
  });
};

/**
 * Tracks when a user clicks to create a new chatbot
 *
 * @param {Object} clickData - Object containing click details
 * @param {string} clickData.user_id - User ID or email
 */
export const trackCreateChatbotClick = (clickData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Push the click data
  window.dataLayer.push({
    event: "click_create_chatbot",
    user_id: clickData.user_id || ''
  });
};

/**
 * Tracks when a user submits a chatbot name
 *
 * @param {Object} nameData - Object containing chatbot name details
 * @param {string} nameData.chatbot_name - Name of the chatbot
 * @param {string} nameData.user_id - User ID or email
 */
export const trackSubmitChatbotName = (nameData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Push the name data
  window.dataLayer.push({
    event: "submit_chatbot_name",
    chatbot_name: nameData.chatbot_name || '',
    user_id: nameData.user_id || ''
  });
};

/**
 * Tracks when a user uploads chatbot memory (files/URLs)
 *
 * @param {Object} uploadData - Object containing upload details
 * @param {boolean} uploadData.has_uploaded_url - Whether URLs were uploaded
 * @param {boolean} uploadData.has_uploaded_file - Whether files were uploaded
 * @param {string} uploadData.user_id - User ID or email
 */
export const trackUploadChatbotMemory = (uploadData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Push the upload data
  window.dataLayer.push({
    event: "upload_chatbot_memory",
    has_uploaded_url: uploadData.has_uploaded_url || false,
    has_uploaded_file: uploadData.has_uploaded_file || false,
    user_id: uploadData.user_id || ''
  });
};

/**
 * Tracks when a user selects a chatbot personality
 *
 * @param {Object} personalityData - Object containing personality details
 * @param {string} personalityData.personality_type - Type of personality selected
 * @param {string} personalityData.user_id - User ID or email
 */
export const trackSelectChatbotPersonality = (personalityData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Push the personality data
  window.dataLayer.push({
    event: "select_chatbot_personality",
    personality_type: personalityData.personality_type || '',
    user_id: personalityData.user_id || ''
  });
};

/**
 * Tracks when a user shares a chatbot
 *
 * @param {Object} shareData - Object containing share details
 * @param {string} shareData.chatbot_id - ID of the chatbot being shared
 * @param {string} shareData.share_method - Method used for sharing (e.g. "bubble", "iframe", "direct-link")
 * @param {string} shareData.user_id - User ID or email
 */
export const trackShareChatbot = (shareData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  // Push the share data
  window.dataLayer.push({
    event: "share_chatbot",
    chatbot_id: shareData.chatbot_id || '',
    share_method: shareData.share_method || '',
    user_id: shareData.user_id || ''
  });
};

/**
 * Tracks addon subscription events in the dataLayer
 *
 * @param {Object} addonData - Object containing addon subscription details
 * @param {string} addonData.user_id - User ID
 * @param {string} addonData.email - User email
 * @param {string} addonData.subscription_id - The subscription/order ID
 * @param {string} addonData.addon_name - Name of the addon (e.g. "additional_storage", "more_seats")
 * @param {number} addonData.addon_price - Price of the addon
 * @param {string} addonData.billing_cycle - Billing cycle (e.g. "monthly", "yearly")
 * @param {string} addonData.payment_method - Payment method (e.g. "credit card", "paypal")
 * @param {string} addonData.base_tier_name - The base tier name to which the addon is being added
 */
export const trackAddonSubscription = (addonData) => {
  if (typeof window === 'undefined') return;

  // Format subscription date in UTC
  const currentDate = new Date();
  const formattedSubscriptionDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Clear the previous ecommerce object
  window.dataLayer.push({ ecommerce: null });

  // Push the addon subscription data
  window.dataLayer.push({
    event: "addon_subscription",
    ecommerce: {
      subscription_id: addonData.subscription_id || '',
      user_id: addonData.user_id || '',
      email: addonData.email || '',
      addon_name: addonData.addon_name || '',
      addon_price: addonData.addon_price || 0,
      subscription_date: formattedSubscriptionDate
    }
  });
};

/**
 * Tracks subscription renewal events in the dataLayer
 *
 * @param {Object} renewalData - Object containing renewal details
 * @param {string} renewalData.subscription_id - The subscription/order ID
 * @param {string} renewalData.user_id - User ID
 * @param {string} renewalData.email - User email
 * @param {string} renewalData.tier_name - Current tier name (e.g. "advanced", "pro", "enterprise")
 * @param {string} renewalData.billing_cycle - Current billing cycle (e.g. "monthly", "yearly")
 * @param {number} renewalData.billing_price - The price charged for the renewal
 * @param {string} renewalData.payment_method - Payment method (e.g. "credit card", "paypal")
 * @param {string} renewalData.next_payment_date - Formatted date of next payment (MM/DD/YYYY HH:MM:SS)
 * @param {number} renewalData.total_renewals - Total number of renewals including this one
 */
export const trackSubscriptionRenewal = (renewalData) => {
  if (typeof window === 'undefined') return;

  // Format renewal date in UTC
  const currentDate = new Date();
  const formattedRenewalDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  // Clear the previous ecommerce object
  window.dataLayer.push({ ecommerce: null });

  // Push the renewal data
  window.dataLayer.push({
    event: "subscription_renewed",
    ecommerce: {
      subscription_id: renewalData.subscription_id || '',
      user_id: renewalData.user_id || '',
      email: renewalData.email || '',
      tier_name: renewalData.tier_name || '',
      billing_cycle: renewalData.billing_cycle || 'monthly',
      billing_price: renewalData.billing_price || 0,
      payment_method: renewalData.payment_method || 'credit card',
      renewal_date: formattedRenewalDate,
      next_payment_date: renewalData.next_payment_date || '',
      total_renewals: renewalData.total_renewals || 1
    }
  });
};

/**
 * Tracks when a user clicks to create a new voice agent
 * 
 * @param {Object} clickData - Object containing click details
 * @param {string} clickData.user_id - User ID or email
 */
export const trackCreateVoiceClick = (clickData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "create_voice_clicked",
    user_id: clickData.user_id || ''
  });
};

/**
 * Tracks when a voice agent is successfully created
 * 
 * @param {Object} voiceData - Object containing voice creation details
 * @param {string} voiceData.user_id - User ID
 * @param {string} voiceData.email - User email
 * @param {string} voiceData.voice_id - ID of the created voice agent
 * @param {string} voiceData.voice_name - Name of the created voice agent
 * @param {string} voiceData.voice_provider - Provider of the voice (e.g. "elevenlabs", "openai")
 */
export const trackVoiceCreated = (voiceData) => {
  if (typeof window === 'undefined') return;

  const currentDate = new Date();
  const formattedCreationDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "voice_created",
    user_id: voiceData.user_id || '',
    email: voiceData.email || '',
    voice_id: voiceData.voice_id || '',
    voice_name: voiceData.voice_name || '',
    voice_provider: voiceData.voice_provider || '',
    creation_date: formattedCreationDate
  });
};

/**
 * Tracks when a user clicks the purchase number button
 * 
 * @param {Object} clickData - Object containing click details
 * @param {string} clickData.user_id - User ID or email
 * @param {string} clickData.phone_number - The phone number being purchased
 */
export const trackPurchaseNumberButtonClick = (clickData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "purchase_number_button_clicked",
    user_id: clickData.user_id || '',
    phone_number: clickData.phone_number || ''
  });
};

/**
 * Tracks when a user clicks the buy now button in the purchase modal
 * 
 * @param {Object} clickData - Object containing click details
 * @param {string} clickData.user_id - User ID or email
 * @param {string} clickData.phone_number - The phone number being purchased
 * @param {number} clickData.price - The price of the phone number
 */
export const trackBuyNumberClick = (clickData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "buy_number_clicked",
    user_id: clickData.user_id || '',
    phone_number: clickData.phone_number || '',
    price: clickData.price || 0
  });
};

/**
 * Tracks when a phone number is successfully purchased
 * 
 * @param {Object} purchaseData - Object containing purchase details
 * @param {string} purchaseData.user_id - User ID
 * @param {string} purchaseData.email - User email
 * @param {string} purchaseData.phone_number - The purchased phone number
 * @param {number} purchaseData.price - The price paid
 * @param {string} purchaseData.payment_method - Payment method used
 */
export const trackNumberPurchasedSuccess = (purchaseData) => {
  if (typeof window === 'undefined') return;

  const currentDate = new Date();
  const formattedPurchaseDate = formatDateWithTimeUTC(currentDate);

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "number_purchased_successfully",
    user_id: purchaseData.user_id || '',
    email: purchaseData.email || '',
    phone_number: purchaseData.phone_number || '',
    price: purchaseData.price || 0,
    payment_method: purchaseData.payment_method || '',
    purchase_date: formattedPurchaseDate
  });
};

/**
 * Tracks when a user clicks to view voice insights
 * 
 * @param {Object} clickData - Object containing click details
 * @param {string} clickData.user_id - User ID or email
 * @param {string} clickData.voice_id - ID of the voice agent
 */
export const trackVoiceInsightsClick = (clickData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "voice_insights_clicked",
    user_id: clickData.user_id || '',
    voice_id: clickData.voice_id || ''
  });
};

/**
 * Tracks when a user clicks to view voice conversations
 * 
 * @param {Object} clickData - Object containing click details
 * @param {string} clickData.user_id - User ID or email
 * @param {string} clickData.voice_id - ID of the voice agent
 */
export const trackVoiceConversationsClick = (clickData) => {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];

  window.dataLayer.push({
    event: "voice_conversations_clicked",
    user_id: clickData.user_id || '',
    voice_id: clickData.voice_id || ''
  });
};
