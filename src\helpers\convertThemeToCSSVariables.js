import { DANTE_THEME_CHAT } from "@/constants";

const hex2rgb = (hex) => {
  const [r, g, b] = [1, 3, 5].map((offset) => parseInt(hex?.slice(offset, offset + 2), 16));
  return `${r}, ${g}, ${b}`;
};

export const convertThemeToCSSVariablesObj = (theme) => {
  const themeVars = {};

  // Converter as cores
  if (theme.color) {
    Object.entries(theme.color).forEach(([key, value]) => {
      themeVars[`--color-${key}`] = hex2rgb(value);
    });
  }

  const { family, size } = theme.font || {};
  if (family) themeVars['--dt-font-family'] = `'${family || DANTE_THEME_CHAT.font_name}'`;
  if (size) themeVars['--dt-font-size'] = `${size}px`;

  const colorVariables = {
    '--dt-color-element-2': 'rgba(var(--color-element), 0.02)',
    '--dt-color-element-5': 'rgba(var(--color-element), 0.05)',
    '--dt-color-element-10': 'rgba(var(--color-element), 0.1)',
    '--dt-color-element-20': 'rgba(var(--color-element), 0.2)',
    '--dt-color-element-50': 'rgba(var(--color-element), 0.5)',
    '--dt-color-element-75': 'rgba(var(--color-element), 0.75)',
    '--dt-color-element-100': 'rgba(var(--color-element), 1)',

    '--dt-color-brand-5': 'rgba(var(--color-brand), 0.05)',
    '--dt-color-brand-10': 'rgba(var(--color-brand), 0.1)',
    '--dt-color-brand-40': 'rgba(var(--color-brand), 0.4)',
    '--dt-color-brand-60': 'rgba(var(--color-brand), 0.6)',
    '--dt-color-brand-50': 'rgba(var(--color-brand), 0.5)',
    '--dt-color-brand-100': 'rgba(var(--color-brand), 1)',

    '--dt-color-surface-0': 'rgba(var(--color-surface), 0)',
    '--dt-color-surface-15': 'rgba(var(--color-surface), 0.15)',
    '--dt-color-surface-100': 'rgba(var(--color-surface), 1)',

    '--dt-color-alert-5': 'rgba(var(--color-alert), 0.05)',
    '--dt-color-alert-100': 'rgba(var(--color-alert), 1)',

    '--dt-color-positive-5': 'rgba(var(--color-positive), 0.05)',
    '--dt-color-positive-100': 'rgba(var(--color-positive), 1)',

    '--dt-color-negative-5': 'rgba(var(--color-negative), 0.05)',
    '--dt-color-negative-100': 'rgba(var(--color-negative), 1)'
  };

  return { ...themeVars, ...colorVariables };
};

const convertThemeToCSSVariablesStyle = (theme) => {
  const themeVars = convertThemeToCSSVariablesObj(theme);
  return Object.entries(themeVars)
    .map(([key, value]) => `${key}: ${value};`)
    .join('\n');
};

export default convertThemeToCSSVariablesStyle;
