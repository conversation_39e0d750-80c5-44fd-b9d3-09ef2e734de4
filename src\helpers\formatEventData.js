const formatEventData = (data) => {
  return JSON.stringify(data)
    .replace(/"/g, '') // Remove all double quotes
    .replaceAll('_DANTE_NEW_LINE_', '\n') // Replace custom newline with actual newline
    .replace(/\/([^/]+)\//g, '<b>$1</b>') // Wrap text between slashes with <b> tags
    .replace(/\\/g, '"') // Replace backslashes with double quotes
    .replaceAll('_DANTE_DOUBLE_QUOTE_', '"') // Replace placeholder for double quotes
    .replaceAll('_DANTE_SINGLE_QUOTE_', "'"); // Replace placeholder for single quotes
};

export default formatEventData;
