import { useChatbotStore } from '@/stores/chatbot/chatbotStore';

import { StepEnum } from '../components/Chatbot/Create/StepEnum';

const generateInitialMessageDynamically = () => {
  const currentStep = useChatbotStore.getState().currentStep;
  switch (currentStep) {
    case StepEnum.CHATBOT_NAME:
      return {
        role: 'assistant',
        content:
          "👋 Hi there! Let’s start by giving your AI Chatbot a name.",
        type: 'welcome_message',
        status: 'completed',
      };
    case StepEnum.KNOWLEDGE:
      return {
        role: 'assistant',
        content:
          "🧠 Ready to expand my memory. <br /> <br />Follow this guide to train more: <a href='https://www.dante-ai.com/guides/uploading-files' target='_blank'>Train your AI Chatbot</a>",
        type: 'welcome_message',
        status: 'completed',
        link: {
          url: 'https://www.dante-ai.com/guides/uploading-files',
          label: 'Chatbot knowledge',
          target: '_blank'
        }
      };
    case StepEnum.PERSONALITY_CREATE:
      return {
        role: 'assistant',
        content:
          "🗣️ Time to update how I respond. <br /><br />Want me to sound friendly, formal, or fun? This guide shows you how: <a href='https://www.dante-ai.com/guides/chatbot-personality' target='_blank'>Chatbot Personality</a>",
        type: 'welcome_message',
        status: 'completed',
        link: {
          url: 'https://www.dante-ai.com/guides/chatbot-personality',
          label: 'Chatbot personality',
          target: '_blank'
        }
      };
    case StepEnum.REVIEW_CREATE:
      return {
        role: 'assistant',
        content:
          "✅ Review and confirm your AI Chatbot setup <br /><br /> Make sure everything looks good. When you’re ready, click Finish to build your chatbot. You can edit it anytime after.",
        type: 'welcome_message',
        status: 'completed',
        link: {
          url: 'https://www.dante-ai.com/guides/chatbot-personality',
          label: 'Chatbot personality',
          target: '_blank'
        }
      };
    case StepEnum.SETUP:
      return {
        role: 'assistant',
        content:
          "It looks like I'm about to update my core settings—exciting! <br /> Let me share a link to a great guide on how to do this right. It will surely help. <a href='https://www.dante-ai.com/guides/chatbot-personality' target='_blank'>Chatbot setup</a>",
        type: 'welcome_message',
        status: 'completed',
        link: {
          url: 'https://www.dante-ai.com/guides/chatbot-personality',
          label: 'Chatbot setup',
          target: '_blank'
        }
      };
    case StepEnum.STYLING:
      return {
        role: 'assistant',
        content:
          "I’m ready to match you brand!  <br />Here’s a quick guide to help: <a href='https://www.dante-ai.com/guides/customizing-a-ai-chatbot' target='_blank'>White-labeling your AI Chatbot</a>",
        type: 'welcome_message',
        status: 'completed',
        link: {
          url: 'https://www.dante-ai.com/guides/customizing-a-ai-chatbot',
          label: 'Chatbot styling',
          target: '_blank'
        }
      };
    case StepEnum.POWER_UPS:
      return {
        role: 'assistant',
        content:
          "It looks like I'm about to update my power ups—exciting! <br /> Let me share a link to a great guide on how to do this right. It will surely help. <a href='https://www.dante-ai.com/guides/chatbot-personality' target='_blank'>Chatbot power ups</a>",
        type: 'welcome_message',
        status: 'completed',
        link: {
          url: 'https://www.dante-ai.com/guides/chatbot-personality',
          label: 'Chatbot power ups',
          target: '_blank'
        }
      };
    default:
      return {
        role: 'assistant',
        content: '👋 Hi there, how may I help you today?',
        type: 'welcome_message',
        status: 'completed'
      };
  }
};

export default generateInitialMessageDynamically;
