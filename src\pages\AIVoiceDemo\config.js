// Configuration for AI Voice Demo

// Voice options with IDs from the backend
export const voiceOptions = {
  openai: [
    { id: "open-ai-alloy", name: "<PERSON><PERSON>", description: "Versatile and balanced", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/openai/open-ai-alloy.mp3" },
    { id: "open-ai-echo", name: "<PERSON>", description: "Soft and melodic", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/openai/open-ai-echo.mp3" },
    { id: "open-ai-fable", name: "Fable", description: "Warm and engaging", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/openai/open-ai-fable.mp3" },
    { id: "open-ai-onyx", name: "Onyx", description: "Strong and confident", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/openai/open-ai-onyx.mp3" }
  ],
  cartesia: [
    { id: "607167f6-9bf2-473c-accc-ac7b3b66b30b", name: "<PERSON>", description: "Professional and friendly", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/cartesia/607167f6-9bf2-473c-accc-ac7b3b66b30b.mp3" },
    { id: "bf0a246a-8642-498a-9950-80c35e9276b5", name: "Sophie", description: "Clear and articulate", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/cartesia/bf0a246a-8642-498a-9950-80c35e9276b5.mp3" },
    { id: "729651dc-c6c3-4ee5-97fa-350da1f88600", name: "Pleasant Man", description: "Warm and trustworthy", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/cartesia/729651dc-c6c3-4ee5-97fa-350da1f88600.mp3" },
    { id: "63406bbd-ce1b-4fff-8beb-86d3da9891b9", name: "Grant", description: "Authoritative and clear", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/cartesia/63406bbd-ce1b-4fff-8beb-86d3da9891b9.mp3" }
  ],
  elevenlabs: [
    { id: "A4WN0z34eYYp20Dc5Lkq", name: "Kenneth", description: "Professional and clear", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/elevenlabs/A4WN0z34eYYp20Dc5Lkq.mp3" },
    { id: "vBKc2FfBKJfcZNyEt1n6", name: "Finn", description: "Friendly and natural", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/elevenlabs/vBKc2FfBKJfcZNyEt1n6.mp3" },
    { id: "fDeOZu1sNd7qahm2fV4k", name: "Luna", description: "Warm and approachable", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/elevenlabs/fDeOZu1sNd7qahm2fV4k.mp3" },
    { id: "c51VqUTljshmftbhJEGm", name: "Emily", description: "Engaging and dynamic", preview_url: "https://dante-ai-voice-previews.lon1.digitaloceanspaces.com/elevenlabs/c51VqUTljshmftbhJEGm.mp3" }
  ]
};

// Knowledge base options
export const kbOptions = [
  { id: "fde8f17a-4f44-4ca4-82fe-072b1d430c76", name: "Test Intrawiki" },
  { id: "9c0294ed-8b0e-46b5-9a65-21d689c8f747", name: "telegrafi.com" }
];

// Default values
export const defaultSettings = {
  selectedVoice: "open-ai-alloy", // Default voice ID
  kbId: "fde8f17a-4f44-4ca4-82fe-072b1d430c76", // Test Intrawiki by default
  initialMessage: "Hello, how can I help you today?",
  authToken: "demo-token",
  userEmail: "", // Default empty string for user email
  voice_instructions: "" // Default empty string for OpenAI voice instructions
};
