import React, { useEffect, useState } from "react";
import DCheckbox from "@/components/Global/DCheckbox";
import DInput from "@/components/Global/DInput/DInput";
import DSelect from "@/components/Global/DSelect";
import DTable from "@/components/Global/DTable";
import DButton from "@/components/Global/DButton";
import useDebounce from "@/hooks/useDebounce";
import useDanteApi from "@/hooks/useDanteApi";
import { getUsersAdmin, updateUserMigrationRule } from "@/services/admin.service";
import useToast from "@/hooks/useToast";

const AdminDashboard = () => {
  const [rows, setRows] = useState([]);
  // Search state used for API call (debounced)
  const [searchQuery, setSearchQuery] = useState("");
  const [pagination, setPagination] = useState({ limit: 50, offset: 0 });
  // Sort state: order_by is one of the allowed strings, ascending is a boolean.
  const [sort, setSort] = useState({ order_by: "date_created", ascending: true });
  // Store field changes per user id.
  const [changedFields, setChangedFields] = useState({});

  const { addSuccessToast } = useToast();

  const { data: users, isLoading, error } = useDanteApi(
    getUsersAdmin,
    [searchQuery, pagination.offset, sort.order_by, sort.ascending],
    {},
    {
      email__ilike: searchQuery,
      limit: pagination.limit,
      offset: pagination.offset,
      order_by: sort.order_by,
      ascending: sort.ascending ? 1 : 0,
    }
  );

  // Debounce updating the search query and reset pagination offset.
  const debouncedSetSearchQuery = useDebounce((value) => {
    setSearchQuery(value);
    setPagination((prev) => ({ ...prev, offset: 0 }));
  }, 500);

  // Update changedFields per user when a checkbox changes.
  const handleCheckboxChange = (userId, key, value) => {
    setChangedFields((prev) => ({
      ...prev,
      [userId]: { ...prev[userId], [key]: value },
    }));
  };

  const tableColumns = [
    { label: "Email", key: "email", showName: true, minWidth: "min-w-10" },
    {
      label: "Old design",
      key: "old_design",
      showName: true,
      minWidth: "min-w-10",
    },
    {
      label: "New design",
      key: "new_design",
      showName: true,
      minWidth: "min-w-10",
    },
    {
      label: "Migration wave",
      key: "migration_wave",
      showName: true,
      minWidth: "min-w-10",
    },
    {
      label: "Actions",
      key: "actions",
      showName: true,
      minWidth: "min-w-10",
    },
  ];

  // Update a user using its id and the changed fields for that user.
  const handleEditUser = async (userId) => {
    try {
      const response = await updateUserMigrationRule({
        id: userId,
        ...changedFields[userId],
      });
      if (response.status === 200) {
        addSuccessToast("User updated successfully");
      }
    } catch (e) {
      console.error(e);
    }
  };

  // Handle pagination changes (next/prev).
  const handlePageChange = (direction) => {
    setPagination((prev) => {
      let newOffset = prev.offset;
      if (direction === "next") {
        newOffset = prev.offset + prev.limit;
      } else if (direction === "prev") {
        newOffset = Math.max(0, prev.offset - prev.limit);
      }
      return { ...prev, offset: newOffset };
    });
  };

  useEffect(() => {
    if (users) {
      setRows(
        users.results.map((user) => ({
          id: user.id,
          email: user.email,
          old_design: (
            <DCheckbox
              checked={
                changedFields[user.id]?.old_design ?? user.old_design
              }
              onChange={(newValue) =>
                handleCheckboxChange(user.id, "old_design", newValue)
              }
            />
          ),
          new_design: (
            <DCheckbox
              checked={
                changedFields[user.id]?.new_design ?? user.new_design
              }
              onChange={(newValue) =>
                handleCheckboxChange(user.id, "new_design", newValue)
              }
            />
          ),
          migration_wave:
            changedFields[user.id]?.migration_wave ?? user.migration_wave,
          actions: (
            <DButton onClick={() => handleEditUser(user.id)}>Save</DButton>
          ),
        }))
      );
    }
  }, [users, changedFields]);

  return (
    <div className="w-full h-full bg-white rounded-size1 p-size5 flex flex-col gap-size5">
      <div className="flex gap-size2">
        <DInput
          label="Search"
          placeholder="Search"
          className="w-full"
          value={searchQuery}
          onChange={(e) => {
            // Use the debounced function to update the API search query and reset pagination.
            debouncedSetSearchQuery(e.target.value);
          }}
        />
        {/* Select for sort field (order_by) */}
        <DSelect
          label="Sort Field"
          value={sort.order_by}
          options={[
            { label: "Date Created", value: "date_created" },
            { label: "Date Updated", value: "date_updated" },
            { label: "Migration Wave", value: "migration_wave" },
          ]}
          onChange={(value) => {
            setSort((prev) => ({
              ...prev,
              order_by: value,
            }));
            setPagination((prev) => ({ ...prev, offset: 0 }));
          }}
        />
        {/* Select for sort order (ascending/descending) */}
        <DSelect
          label="Sort Order"
          value={sort.ascending ? "1" : "0"}
          options={[
            { label: "Ascending", value: "1" },
            { label: "Descending", value: "0" },
          ]}
          onChange={(value) => {
            setSort((prev) => ({
              ...prev,
              ascending: value === "1",
            }));
            setPagination((prev) => ({ ...prev, offset: 0 }));
          }}
        />
      </div>
      <div className="overflow-y-auto no-scrollbar">
        <DTable columns={tableColumns} data={rows} />
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          disabled={pagination.offset === 0}
          onClick={() => handlePageChange("prev")}
          className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
        >
          Previous
        </button>
        <div>
          Page {Math.floor(pagination.offset / pagination.limit) + 1}
        </div>
        <button
          disabled={users?.results?.length < pagination.limit}
          onClick={() => handlePageChange("next")}
          className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default AdminDashboard;