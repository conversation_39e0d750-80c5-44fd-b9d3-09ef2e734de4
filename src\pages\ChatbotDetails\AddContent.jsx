import { useState } from 'react';
import { useParams } from 'react-router-dom';
import DButton from '@/components/Global/DButton';
import DCheckbox from '@/components/Global/DCheckbox';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import DUpload from '@/components/Global/DUpload';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import FilesIcon from '@/components/Global/Icons/FilesIcon';
import LinkIcon from '@/components/Global/Icons/LinkIcon';
import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';
import * as chatbotService from '@/services/chatbot.service';
import AddIcon from '@/components/Global/Icons/AddIcon';
import isValidFileType from '@/helpers/validateFileType';
import useToast from '@/hooks/useToast';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import { useUserStore } from '@/stores/user/userStore';
import { MAX_URLS_PER_ENTIRE_DOMAIN } from '@/constants';

const AddContent = ({
  isOpen,
  onClose,
  refetch,
  setOpenRetrain,
  setAddedContent,
}) => {
  const params = useParams();
  const [activeTab, setActiveTab] = useState('files');
  const chatbotData = useCreateChatbotStore((state) => state.chatbotData);
  const setChatbotData = useCreateChatbotStore((state) => state.setChatbotData);
  const [loading, setLoading] = useState(false);
  let formData = new FormData();
  const { addErrorToast } = useToast();

  const handleUploadFiles = (e) => {
    if (e.target.files.length > 0) {
      if (isValidFileType(e.target.files[0])) {
        setChatbotData('chatbotFiles', [
          ...chatbotData.chatbotFiles,
          ...e.target.files,
        ]);
      } else {
        addErrorToast({ message: 'Invalid file type' });
      }
    }
  };

  const handleDeleteUrl = (index) => {
    setChatbotData(
      'chatbotUrls',
      chatbotData.chatbotUrls.filter((_, i) => i !== index)
    );
  };

  const handleAddUrl = () => {
    setChatbotData('chatbotUrls', [
      ...chatbotData.chatbotUrls,
      { url: '', sweepEntireDomain: false },
    ]);
  };

  const handleUploadBulkUrls = (e) => {
    const files = e.target.files;
    setChatbotData('chatbotBulkUploadedUrls', [
      ...chatbotData.chatbotBulkUploadedUrls,
      ...files,
    ]);

    if (files.length > 0) {
      Array.from(files).forEach((file) => {
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const reader = new FileReader();

        reader.onload = (event) => {
          const content = event.target.result;
          let urls = [];
          if (fileExtension === 'rtf') {
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            const matches = content.match(urlRegex) || [];
            const uniqueUrls = [...new Set(matches)];
            urls = uniqueUrls;
          } else {
            urls = content
              .replace(/ /g, '\n')
              .split('\n')
              .map((url) => url.trim())
              .filter((url) => url.length > 0);
          }

          const validUrls = urls.filter((url) => {
            return isValidUrl(url);
          });

          setChatbotData('chatbotUrls', [
            ...chatbotData.chatbotUrls,
            ...validUrls.map((url) => ({ url, sweepEntireDomain: true })),
          ]);
        };
        reader.onerror = () => {
          console.error('Error reading file:', file.name);
        };
        reader.readAsText(file);
      });
    }
  };

  const handleChangeUrl = (e, index) => {
    setChatbotData(
      'chatbotUrls',
      chatbotData.chatbotUrls.map((url, i) =>
        i === index
          ? { ...url, url: e.target.value, max_urls: url.max_urls }
          : url
      )
    );
  };

  const isValidUrl = (string) => {
    try {
      if (string.startsWith('www.')) {
        string = 'https://' + string;
      }
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const updateChatbotFiles = async () => {
    try {
      const res = await chatbotService.updateChatbotFiles(params.id, formData);
      return res;
    } catch (error) {
      console.log(error);
    }
  };

  const updateChatbotUrls = async () => {
    try {
      const res = await chatbotService.updateChatbotUrls(
        params.id,
        chatbotData.chatbotUrls
          .filter((url) => url.url !== '')
          .map((url) => ({
            url: url.url,
            max_urls: url.sweepEntireDomain ? MAX_URLS_PER_ENTIRE_DOMAIN : 1,
          }))
      );
      return res;
    } catch (error) {
      console.log(error);
    }
  };

  const updateChatbotAll = async () => {
    try {
      const res = await chatbotService.updateChatbotAll(
        params.id,
        chatbotData.chatbotUrls[0].url !== ''
          ? chatbotData.chatbotUrls.map((url) => url.url)
          : [''],
        formData,
        chatbotData.chatbotUrls[0].url !== ''
          ? chatbotData.chatbotUrls.map((url) =>
              url.sweepEntireDomain ? MAX_URLS_PER_ENTIRE_DOMAIN : 1
            )
          : []
      );
      return res;
    } catch (error) {
      console.log(error);
    }
  };
  const handleAddContent = async () => {
    setLoading(true);
    chatbotData.chatbotFiles.forEach((file) => {
      formData.append('files', file);
    });
  
    // Validate URLs: Ensure that the URL is not just "https://"
    const validUrls = chatbotData.chatbotUrls.filter(
      (urlObj) => urlObj.url && urlObj.url !== '' && urlObj.url !== "https://"
    );
  
    try {
      let response;
      let updateType = '';

      if (
        validUrls.length > 0 && 
        chatbotData.chatbotFiles.length > 0
      ) {
        // If both valid URLs and files exist
        response = await updateChatbotAll();
        updateType = 'files_and_urls';
      } else if (
        validUrls.length > 0 && 
        chatbotData.chatbotFiles.length === 0
      ) {
        // If valid URLs exist but no files
        response = await updateChatbotUrls();
        updateType = 'urls';
      } else if (
        chatbotData.chatbotFiles.length > 0 &&
        validUrls.length === 0
      ) {
        // If only files exist and no valid URLs
        response = await updateChatbotFiles();
        updateType = 'files';
      }
  
      if (response?.status === 200) {
        // Track customized-content event
        const user = useUserStore.getState().user;
        await trackKlaviyoEvent('customized-content', { 
          chatbot_id: params.id,
          update_type: updateType
        });

        setAddedContent(response.data.results);
        setChatbotData('chatbotUrls', []);
        setChatbotData('chatbotFiles', []);
        setChatbotData('chatbotBulkUploadedUrls', []);
        refetch();
        onClose();
        setOpenRetrain(true);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  

  const handleDeleteFile = (index) => {
    const newFiles = [...chatbotData.chatbotFiles];
    newFiles.splice(index, 1);
    setChatbotData('chatbotFiles', newFiles);
  };

  const handleChangeSweepEntireDomain = (checked, url) => {
    setChatbotData('chatbotUrls', chatbotData.chatbotUrls.map((u, i) =>
      u.url === url ? { ...u, sweepEntireDomain: checked } : u
    ));
  };

  return (
    <DModal title="Add content" isOpen={isOpen} onClose={onClose}
      className='!min-w-[500px]'
      footer={
        <DButton variant="dark" size="md" onClick={handleAddContent} fullWidth loading={loading}>
        Add content
      </DButton>
      }
    >
      <div className="flex flex-col gap-size5">
        <div className="flex gap-size1">
          <button
            className={`dbutton flex w-full items-center justify-center rounded-size2 h-9 gap-size0 ${
              activeTab === 'files' ? 'bg-purple-100' : 'bg-grey-1'
            }`}
            onClick={() => setActiveTab('files')}
          >
            <FilesIcon
              className={`${
                activeTab === 'files' ? 'text-purple-300' : 'text-black'
              }`}
            />
            <span
              className={`${
                activeTab === 'files' ? 'text-purple-300' : 'text-black'
              } text-xs`}
            >
              Upload Files
            </span>
          </button>
          <button
            className={`dbutton flex w-full items-center justify-center rounded-size2 h-9 gap-size0 ${
              activeTab === 'url' ? 'bg-purple-100' : 'bg-grey-1'
            }`}
            onClick={() => setActiveTab('url')}
          >
            <LinkIcon
              className={`${
                activeTab === 'url' ? 'text-purple-300' : 'text-black'
              }`}
            />
            <span
              className={`${
                activeTab === 'url' ? 'text-purple-300' : 'text-black'
              } text-xs`}
            >
              Add URLs
            </span>
          </button>
        </div>
        {activeTab === 'files' && (
          <div className="flex flex-col gap-size1">
            <DUpload
              note="Max. size 128mb"
              btnClassName="bg-grey-5 !text-black"
              onChangeFile={(e) => handleUploadFiles(e)}
              accept=".pdf,.json,.xml,.docx,.doc,.txt,.csv,.xlsx,.ppt,.pptx,.pptx_zip,.jpeg,.png,.markdown,.epub,.mbox,.rtf,.html"
            />
            <p className="text-grey-20 text-xs font-light">
              *Supported file types: PDF, JSON, XML, DOCX, DOC, TXT, CSV, XLSX,
              PPT, PPTX, PPTX_ZIP, JPEG, PNG, MARKDOWN, EPUB, MBOX, RTF, HTML
            </p>
            {chatbotData.chatbotFiles.length > 0 &&
              chatbotData.chatbotFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex flex-col gap-size0 mt-size3 w-full"
                >
                  <div className="border-b border-b-grey5 p-size1 flex items-center justify-between">
                    <div className="flex gap-size1 items-center">
                      <FilesIcon className="w-[30px] h-[30px]" />
                      <div className="flex flex-col">
                        <span className="text-base font-medium tracking-tight ">
                          {file.name}
                        </span>
                        <div className="flex gap-size0 items-center text-[10px] font-regular tracking-tight text-grey-20 h-[11px] w-full">
                          <span className="truncate max-w-[300px]">
                            {file.type}
                          </span>
                          <div className="w-px h-[11px] bg-grey-20"></div>
                          <span className="whitespace-nowrap">
                            {file.size >= 1024 * 1024
                              ? `${(file.size / (1024 * 1024)).toFixed(2)} MB`
                              : `${(file.size / 1024).toFixed(2)} KB`}
                          </span>
                        </div>
                      </div>
                    </div>
                    <button onClick={() => handleDeleteFile(index)} className="dbutton">
                      <DeleteIcon />
                    </button>
                  </div>
                </div>
              ))}
          </div>
        )}
        {activeTab === 'url' && (
          <div className="flex flex-col gap-size5">
            {chatbotData.chatbotUrls.map((url, index) => (
              <div key={index} className="flex flex-col gap-size1">
                <div className="flex gap-size1">
                  <DInput
                    icon={<LinkIcon />}
                    placeholder="https://"
                    value={url.url}
                    onChange={(e) => handleChangeUrl(e, index)}
                  />
                  <button
                    className="dbutton p-size2 bg-grey-2 rounded-size1"
                    onClick={() => handleDeleteUrl(index)}
                  >
                    <DeleteIcon />
                  </button>
                </div>
                <DCheckbox
                  label="Sweep the entire domain"
                  className="pl-size2"
                  checked={url.sweepEntireDomain}
                  onChange={(checked) => handleChangeSweepEntireDomain(checked, url.url)}
                />
              </div>
            ))}
            <button
              className="dbutton flex items-center gap-size1 text-sm tracking-tight"
              onClick={handleAddUrl}
            >
              <AddIcon />
              Add URL
            </button>
            <div className="bg-grey-5 w-full h-px"></div>
            <div className="flex flex-col gap-size1">
              <span className="text-base tracking-tight font-medium">
                Bulk Upload
              </span>
              <DUpload
                title="Drop your text file here"
                subtitle="Only one URL per line"
                btnClassName="bg-grey-5 !text-black"
                onChangeFile={(e) => handleUploadBulkUrls(e)}
              />
            </div>
            {chatbotData.chatbotBulkUploadedUrls.length > 0 &&
              chatbotData.chatbotBulkUploadedUrls.map((file, index) => (
                <div key={index} className="flex flex-col gap-size0 mt-size3">
                  <div className="border-b border-b-grey5 p-size1 flex items-center justify-between">
                    <div className="flex gap-size1 items-center">
                      <FilesIcon className="w-[30px] h-[30px]" />
                      <div className="flex flex-col">
                        <span className="text-base font-medium tracking-tight">
                          {file.name}
                        </span>
                        <div className="flex gap-size0 items-center text-[10px] font-regular tracking-tight text-grey-20 h-[11px]">
                          <span>{file.type}</span>
                          <div className="w-px h-[11px] bg-grey-20"></div>
                          <span>
                            {file.size >= 1024 * 1024
                              ? `${(file.size / (1024 * 1024)).toFixed(2)} MB`
                              : `${(file.size / 1024).toFixed(2)} KB`}
                          </span>
                        </div>
                      </div>
                    </div>
                    <button onClick={() => handleDeleteBulkUrl(index)} className="dbutton">
                      <DeleteIcon />
                    </button>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>
    </DModal>
  );
};

export default AddContent;
