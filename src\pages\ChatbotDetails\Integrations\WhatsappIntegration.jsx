import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DInput from '@/components/Global/DInput/DInput';
import DIntegrationApp from '@/components/Global/DIntegrationApp';
import DSwitch from '@/components/Global/DSwitch';
import WhatsappColorIcon from '@/components/Global/Icons/WhatsappColorIcon';
import IntegrationApps from '@/components/IntegrationApps';
import useDanteApi from '@/hooks/useDanteApi';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import * as integrationService from '@/services/integration.service';
import * as modelService from '@/services/model.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import { LLM_MODEL_DEFAULT } from '@/constants';
import useToast from '@/hooks/useToast';
import DSelect from '@/components/Global/DSelect';
import { useUserStore } from '@/stores/user/userStore';

const WhatsappIntegration = () => {
  const { addSuccessToast } = useToast();
  const navigate = useNavigate();
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const { user } = useUserStore();
  let params = useParams();

  const { data: graphApiVersions } = useDanteApi(
    integrationService.getGraphAPIVerions
  );

  // Fetch available LLM models based on user's tier
  const { data: llmModels } = useDanteApi(
    modelService.getLLMModels,
    [],
    {},
    { tier_type: user.tier_type }
  );

  // Format LLM models for the select component
  const [formattedLLMModels, setFormattedLLMModels] = useState([]);

  // Initialize state with default values including model_type
  const [whatsappData, setWhatsappData] = useState({
    active: false,
    meta_token: '',
    business_account_id: '',
    business_phone_id: '',
    phone_number: '',
    graph_version: '',
    test_meta_token: '',
    model_type: LLM_MODEL_DEFAULT.value
  });

  const { data, isLoading } = useDanteApi(
    integrationService.getWhatsappIntegration,
    [],
    {},
    params.id
  );

  // Format LLM models for the select component when data is loaded
  useEffect(() => {
    if (llmModels && llmModels.length > 0) {
      const formatted = llmModels.map((model) => ({
        value: model.value,
        label: `${model.label} (${model.credits} credits per response)`
      }));
      setFormattedLLMModels(formatted);
    }
  }, [llmModels]);

  useEffect(() => {
    if (data && Array.isArray(data) && data.length > 0) {
      setWhatsappData(data[0]);
    }
  }, [data]);

  const updateWhatsappIntegration = async () => {
    try {
      // If we have an existing integration, update it, otherwise create a new one
      if (whatsappData.id) {
        // Prepare the payload for updating an existing integration
        const updatePayload = {
          integration_id: whatsappData.id,
          model_type: whatsappData.model_type,
          kb_id: params.id,
          graph_version: whatsappData.graph_version,
          business_account_id: whatsappData.business_account_id,
          business_phone_id: whatsappData.business_phone_id,
          phone_number: whatsappData.phone_number,
          test_meta_token: whatsappData.test_meta_token,
          meta_token: whatsappData.meta_token,
          active: whatsappData.active,
          enabled: whatsappData.enabled
        };

        const response = await integrationService.updateWhatsappIntegration(updatePayload);

        if (response.status === 200) {
          // Check if there's an error message in the response
          if (response.data.err_message) {
            console.error("Error from backend:", response.data.err_message);
            addSuccessToast({
              message: 'WhatsApp integration updated, but with warnings',
            });
          } else {
            addSuccessToast({
              message: 'WhatsApp integration updated successfully',
            });
          }

          // Handle the response
          if (response.data && response.data.integration) {
            setWhatsappData(response.data.integration);
          }
        }
      } else {
        // Create new integration
        const createPayload = {
          kb_id: params.id,
          model_type: whatsappData.model_type,
          graph_version: whatsappData.graph_version,
          business_account_id: whatsappData.business_account_id,
          business_phone_id: whatsappData.business_phone_id,
          phone_number: whatsappData.phone_number,
          test_meta_token: whatsappData.test_meta_token,
          meta_token: whatsappData.meta_token,
          active: whatsappData.active,
          enabled: whatsappData.enabled
        };

        const response = await integrationService.createWhatsappIntegration(createPayload);

        if (response.status === 200) {
          addSuccessToast({
            message: 'WhatsApp integration created successfully',
          });

          // Handle the response which might be an array or a single object
          if (Array.isArray(response.data) && response.data.length > 0) {
            setWhatsappData(response.data[0]);
          } else if (response.data) {
            setWhatsappData(response.data);
          }
        }
      }
    } catch (error) {
      console.error("Error updating WhatsApp integration:", error);
    }
  };

  const toggleWhatsappActivation = async (checked) => {
    // First update the local state immediately for responsive UI
    setWhatsappData({
      ...whatsappData,
      active: checked,
      enabled: checked,
    });

    // Then send the update to the server if we have an ID
    if (whatsappData.id) {
      try {
        const updatePayload = {
          integration_id: whatsappData.id,
          model_type: whatsappData.model_type,
          kb_id: params.id,
          active: checked,
          enabled: checked
        };

        // Use updateWhatsappIntegration to update the activation status
        const response = await integrationService.updateWhatsappIntegration(updatePayload);

        if (response.status === 200) {
          // Check if there's an error message in the response
          if (response.data.err_message) {
            console.error("Error from backend:", response.data.err_message);
            addSuccessToast({
              message: `WhatsApp ${checked ? 'activated' : 'deactivated'}, but with warnings`,
            });
          } else {
            addSuccessToast({
              message: `WhatsApp ${checked ? 'activated' : 'deactivated'} successfully`,
            });
          }

          // Update local state with server response
          if (response.data && response.data.integration) {
            setWhatsappData(response.data.integration);
          }
        }
      } catch (error) {
        console.error("Error toggling WhatsApp activation:", error);
        // Revert local state on error
        setWhatsappData({
          ...whatsappData,
          active: !checked,
          enabled: !checked,
        });
      }
    }
  };

  useEffect(() => {
    setSidebarOpen(false);
    if (window.innerWidth < 768) {
      setLayoutTitle('Chatbot');
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth]);

  return (
    <LayoutRightSidebar
      RightSidebar={() => {
        return <IntegrationApps row={false} />;
      }}
    >
      {() => {
        return (
          <div className="w-full h-[1px] grow overflow-y-auto bg-white rounded-size1 p-size5 flex flex-col gap-size5">
            <div className="flex text-black text-base">
              <a className="text-grey-50" onClick={() => navigate(-1)}>
                Integrations
              </a>{' '}
              /{' '}
              <a href="" className="text-black">
                WhatsApp
              </a>
            </div>
            <div className="w-full h-px bg-grey-5"></div>
            <div className="flex gap-size5">
              <div className="flex flex-col py-size4 px-size7 rounded-size3 border border-grey-5 items-center justify-center size-24">
                <WhatsappColorIcon />
              </div>
              <div className="flex flex-col gap-size3">
                <p className="text-xl font-medium tracking-tight">WhatsApp</p>
                <p className="text-sm text-grey-50">
                  Connect your AI Chatbot to WhatsApp to enable real-time
                  messaging with your customers. Ideal for businesses aiming for
                  a wide-reaching presence on a popular messaging platform.
                </p>
              </div>
            </div>
            <div className="flex gap-size2 p-size5 bg-grey-2 rounded-size2 items-start">
              <div className="flex flex-col gap-size3">
                <p className="text-xl font-medium tracking-tight">
                  Activate WhatsApp
                </p>
                <p className="text-sm ">
                  Connect your AI Chatbot to WhatsApp to enable real-time
                  messaging with your customers. Ideal for businesses aiming for
                  a wide-reaching presence on a popular messaging platform.
                  <a href="https://www.dante-ai.com/guides/whatsapp-integration" target="_blank" rel="noopener noreferrer" className="text-purple-300"> Click here</a> to view the documentation on how to set up WhatsApp
                  integration.{' '}
                </p>
              </div>
              <DSwitch
                checked={whatsappData.active || whatsappData.enabled}
                onChange={toggleWhatsappActivation}
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Permanent Access Token
              </p>
              <DInput
                placeholder="Enter your Permanent Access Token"
                value={whatsappData.meta_token}
                onChange={(e) =>
                  setWhatsappData({
                    ...whatsappData,
                    meta_token: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                WhatsApp Business Account ID
              </p>
              <DInput
                placeholder="Enter your WhatsApp Business Account ID"
                value={whatsappData.business_account_id}
                onChange={(e) =>
                  setWhatsappData({
                    ...whatsappData,
                    business_account_id: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Phone Number ID
              </p>
              <DInput
                placeholder="Enter your Phone Number ID"
                value={whatsappData.business_phone_id}
                onChange={(e) =>
                  setWhatsappData({
                    ...whatsappData,
                    business_phone_id: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Phone Number
              </p>
              <DInput
                placeholder="Enter your Phone Number"
                value={whatsappData.phone_number}
                onChange={(e) =>
                  setWhatsappData({
                    ...whatsappData,
                    phone_number: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Graph API Version
              </p>
              <DSelect
                options={graphApiVersions}
                value={whatsappData.graph_version}
                onChange={(e) =>
                  setWhatsappData({ ...whatsappData, graph_version: e })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                LLM Model
              </p>
              <DSelect
                options={formattedLLMModels}
                value={whatsappData.model_type}
                onChange={(selected) =>
                  setWhatsappData({
                    ...whatsappData,
                    model_type: selected
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Temporary Access Token
              </p>
              <DInput
                placeholder="Enter your Temporary Access Token"
                value={whatsappData.test_meta_token}
                onChange={(e) =>
                  setWhatsappData({
                    ...whatsappData,
                    test_meta_token: e.target.value,
                  })
                }
              />
            </div>
            <DButton variant="dark" onClick={updateWhatsappIntegration}>
              {data ? "Save" : "Connect"}
            </DButton>
          </div>
        );
      }}
    </LayoutRightSidebar>
  );
};

export default WhatsappIntegration;
