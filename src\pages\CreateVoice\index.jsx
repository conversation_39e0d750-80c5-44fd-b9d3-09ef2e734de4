import { useEffect, useState, useRef } from 'react';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import useLayoutStore from '@/stores/layout/layoutStore';
import { voiceSteps } from '@/components/Chatbot/Create/steps';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DSelect from '@/components/Global/DSelect';
import DMultiselect from '@/components/Global/DMultiselect';
import DAlert from '@/components/Global/DAlert';
import DLoaderTraining from '@/components/Global/DLoaderTraining';
import { COMMON_CLASSNAMES } from '@/constants';
import VoicePreview from '@/components/Voice/VoicePreview';
import VoiceSelector from '@/components/Voice/VoiceSelector';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import DSwitchAccordion from '@/components/Global/DSwitchAccordion';
import DTextArea from '@/components/Global/DInput/DTextArea';
import BlobAnimation from '@/components/Voice/BlobAnimation';
import AddIcon from '@/components/Global/Icons/AddIcon';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import { getNumericalFeatureValue } from '@/helpers/tier/featureCheck';
import useModalStore from '@/stores/modal/modalStore';
import InlineGlobalModals from '@/components/InlineGlobalModals';
import { trackCreateVoiceClick, trackVoiceCreated } from '@/helpers/analytics';
import { useUserStore } from '@/stores/user/userStore';

// Import your API functions (they must return Promises)
import {
  createVoice,
  getAllVoiceSettings,
  getAllVoiceSettingsFiltered,
  getTextToVoiceOptions,
  getVoices
} from '@/services/voice.service';

import useDanteApi from '@/hooks/useDanteApi';
import * as chatbotService from '@/services/chatbot.service';
import { getLoadingSounds } from '@/services/voice.service';
import { useNavigate, useLocation } from 'react-router-dom';
import useToast from '@/hooks/useToast';
import { getPhoneNumbers } from '@/services/phoneNumber.service';
import DTransition from '@/components/Global/DTransition';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import DTooltip from '@/components/Global/DTooltip';
import { getChatbotCustomizationById } from '@/services/customization.service';

const CreateVoice = () => {
  // Layout helpers
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);

  const navigate = useNavigate();
  const location = useLocation();

  const { data: chatbotData } = useDanteApi(chatbotService.getChatbots);
  const { data: loadingSounds } = useDanteApi(getLoadingSounds);
  const { data: phoneNumbers } = useDanteApi(getPhoneNumbers);

  const { addErrorToast, addSuccessToast } = useToast();

  // Check if we have a pre-selected chatbot from navigation state
  const preSelectedChatbot = location.state?.preSelectedChatbot;
  const defaultName = location.state?.defaultName;
  // Check if returning from chatbot creation
  const newlyCreatedChatbotId = location.state?.newlyCreatedChatbotId;

  // Define the form steps
  const formSteps = phoneNumbers?.results?.length > 0 ? [
    'name',
    'selected_chatbot',
    'phone_numbers',
    'voice',
    'welcome_message',
    'personality_prompt'
  ] : [
    'name',
    'selected_chatbot',
    'voice',
    'welcome_message',
    'personality_prompt'
  ];

  // Current step index - if we have a pre-selected chatbot, start at the next step
  const [currentStepIndex, setCurrentStepIndex] = useState(preSelectedChatbot ? 1 : 0);

  const [voiceData, setVoiceData] = useState({
    name: defaultName || '',
    welcome_message: '',
    selected_chatbot: preSelectedChatbot || null,
    voice_to_text: {
      label: "Deepgram",
      value: "deepgram"
    },
    llm: { label: 'OpenAI GPT-4o Mini', value: 'gpt_4o_mini' },
    voice_provider: {
      label: "ElevenLabs",
      value: "elevenlabs"
    },
    voice: null,
    loading_sound_enabled: false,
    loading_sound: null,
    personality_prompt: '',
    mode: 'flexible',
    numbers: []
  });

  const [selectedNumbers, setSelectedNumbers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [confirmExit, setConfirmExit] = useState(false);

  const [voiceSettings, setVoiceSettings] = useState({});

  const [providerVoices, setProviderVoices] = useState([]);
  const [errors, setErrors] = useState({});

  // Track which steps have been seen/completed
  const [visitedSteps, setVisitedSteps] = useState(preSelectedChatbot ? [0, 1] : [0]);

  // Map form steps to StepEnum values - defined once at component level for reuse
  const stepEnumMap = {
    'name': 10, // VOICE_NAME
    'selected_chatbot': 11, // CHATBOT_SELECTION
    'voice': 16, // AI_VOICE
    'welcome_message': 17, // GREETING_MESSAGE
    'personality_prompt': 18, // PERSONALITY_PROMPT
    'phone_numbers': 19 // PHONE_NUMBERS
  };

  // Add a new state to trigger voice selector collapse
  const [autoCollapseVoices, setAutoCollapseVoices] = useState(false);

  // Add input refs to reference each form step for scrolling
  const inputRefs = useRef({});
  
  // Remove the glow animation since we're highlighting the title instead
  const [animateStep, setAnimateStep] = useState(null);

  const openInlinePlansModal = useModalStore((state) => state.openInlinePlansModal);
  const [exceedsVoiceLimit, setExceedsVoiceLimit] = useState(false);
  const { user } = useUserStore();

  const isCurrentStepValid = () => {
    const currentField = formSteps[currentStepIndex];

    // Check if the current field is valid
    switch (currentField) {
      case 'name':
        return voiceData.name.trim() !== '';
      case 'selected_chatbot':
        return voiceData.selected_chatbot !== null;
      case 'voice_to_text':
        return voiceData.voice_to_text !== null;
      case 'llm':
        return voiceData.llm !== null;
      case 'voice_provider':
        return voiceData.voice_provider !== null;
      case 'voice':
        return voiceData.voice !== null;
      default:
        return true;
    }
  };

  const goToNextStep = () => {
    if (currentStepIndex < formSteps.length - 1) {
      // Only proceed if current step is valid
      if (!isCurrentStepValid()) {
        // Show an error for the current field
        const currentField = formSteps[currentStepIndex];
        setErrors({
          ...errors,
          [currentField]: `${currentField.replace('_', ' ')} is required.`
        });
        return;
      }

      const nextIndex = currentStepIndex + 1;
      setCurrentStepIndex(nextIndex);

      // Mark this step as visited
      if (!visitedSteps.includes(nextIndex)) {
        setVisitedSteps([...visitedSteps, nextIndex]);
      }
      
      // Trigger auto-collapse when moving from voice step to welcome_message
      if (formSteps[currentStepIndex] === 'voice' && formSteps[nextIndex] === 'welcome_message') {
        setAutoCollapseVoices(true);
      }
      
      // Auto-fetch personality prompt when moving to personality_prompt step
      if (formSteps[nextIndex] === 'personality_prompt' && voiceData.selected_chatbot && !voiceData.personality_prompt) {
        getPersonalityPromptFromChatbot();
      }
      
      // Update progress bar for current step
      updateProgressBar(formSteps[currentStepIndex]);
      
      // We don't need to trigger animation anymore, just scroll
      // Scroll to the next input after a short delay to allow for render
      setTimeout(() => {
        const nextField = formSteps[nextIndex];
        const nextStepRef = inputRefs.current[nextField];
        if (nextStepRef) {
          nextStepRef.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  };

  const goToPreviousStep = () => {
    if (currentStepIndex > 0) {
      const prevIndex = currentStepIndex - 1;
      setCurrentStepIndex(prevIndex);
      
      // Scroll to the previous input
      setTimeout(() => {
        const prevField = formSteps[prevIndex];
        const prevStepRef = inputRefs.current[prevField];
        if (prevStepRef) {
          prevStepRef.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  };

  const isStepVisited = (index) => {
    return visitedSteps.includes(index);
  };

  const validateFields = () => {
    const newErrors = {};

    if (!voiceData.name.trim()) newErrors.name = 'AI Voice Agent Name is required.';
    if (!voiceData.selected_chatbot) newErrors.selected_chatbot = 'Memory selection is required.';
    if (!voiceData.mode) newErrors.mode = 'Voice mode is required.';
    if (!voiceData.voice_to_text) newErrors.voice_to_text = 'Voice-to-text option is required.';
    if (!voiceData.llm) newErrors.llm = 'LLM selection is required.';
    if (!voiceData.voice_provider) newErrors.voice_provider = 'Text-to-voice provider is required.';
    if (!voiceData.voice) newErrors.voice = 'Voice selection is required.';
    if (phoneNumbers?.results?.length > 0 && voiceData.numbers.length === 0) newErrors.phone_numbers = 'Phone numbers are required.';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Update progress bar when current step changes
  useEffect(() => {
    // Only update if we have a valid currentStepIndex
    if (currentStepIndex < 0 || currentStepIndex >= formSteps.length) return;

    // Find the StepEnum ID for the current form step
    const currentFieldId = stepEnumMap[formSteps[currentStepIndex]];

    setProgressBar((prevSteps) => {
      return prevSteps.map((step) => {
        // Find which form field corresponds to this progress step
        const stepFieldKey = Object.keys(stepEnumMap).find(key => stepEnumMap[key] === step.id);

        // If this step isn't part of our active form fields, skip it
        if (!stepFieldKey) return step;
        
        // Skip phone_numbers step if there are no phone numbers
        if (stepFieldKey === 'phone_numbers' && (!phoneNumbers?.results || phoneNumbers.results.length === 0)) {
          return step;
        }

        // Find the index of this field in our form flow
        const stepIndex = formSteps.indexOf(stepFieldKey);

        // Determine step status
        const isCurrentField = stepIndex === currentStepIndex;
        const isPreviousField = stepIndex < currentStepIndex;

        return {
          ...step,
          active: isCurrentField,
          completed: isPreviousField,
          pending: !isCurrentField && !isPreviousField
        };
      });
    });
  }, [currentStepIndex, formSteps]);

  const updateProgressBar = (field) => {
    // Find the field index in our form flow
    const fieldIndex = formSteps.indexOf(field);

    if (fieldIndex === -1) return; // Field not found in formSteps

    // Update the progress bar steps
    setProgressBar((prevSteps) => {
      return prevSteps.map((step) => {
        // Find which form field corresponds to this progress step
        const stepFieldKey = Object.keys(stepEnumMap).find(key => stepEnumMap[key] === step.id);

        // If this step isn't part of our active form fields, skip it
        if (!stepFieldKey) return step;
        
        // Skip phone_numbers step if there are no phone numbers
        if (stepFieldKey === 'phone_numbers' && (!phoneNumbers?.results || phoneNumbers.results.length === 0)) {
          return step;
        }

        // Find the index of this field in our form flow
        const stepIndex = formSteps.indexOf(stepFieldKey);

        if (stepIndex === -1) return step; // Field not in form flow

        // Update step status based on form progression
        if (stepFieldKey === field) {
          // This is the field being updated
          return {
            ...step,
            active: true,
            completed: !!voiceData[field],
            pending: false
          };
        } else if (stepIndex < fieldIndex) {
          // Previous steps should be completed
          return { ...step, completed: true, active: false, pending: false };
        } else if (stepIndex === fieldIndex + 1) {
          // Next step should be active
          return { ...step, completed: false, active: true, pending: false };
        } else {
          // Future steps should be pending
          return { ...step, completed: false, active: false, pending: true };
        }
      });
    });
  };

  const handleCreateVoice = async () => {
    if (!validateFields()) return;
    setIsLoading(true);

    // Mark all steps as completed in the progress bar
    setProgressBar((prevSteps) => {
      return prevSteps.map((step) => {
        // Find which form field corresponds to this progress step
        const stepFieldKey = Object.keys(stepEnumMap).find(key => stepEnumMap[key] === step.id);
        
        // Skip phone_numbers step if there are no phone numbers
        if (stepFieldKey === 'phone_numbers' && (!phoneNumbers?.results || phoneNumbers.results.length === 0)) {
          return step;
        }
        
        return {
          ...step,
          completed: true,
          active: false,
          pending: false
        };
      });
    });

    const payload = {
      name: voiceData.name,
      welcome_message: voiceData.welcome_message,
      kb_id: voiceData.selected_chatbot.value,
      voice_to_text: voiceData.voice_to_text.value,
      llm: voiceData.llm.value,
      text_to_voice: voiceData.voice_provider.value,
      text_to_voice_settings_id: providerVoices.find((voice) => voice.value === voiceData.voice.value)?.value,
      loading_sound_id: voiceData.loading_sound_enabled ? voiceData.loading_sound.value : null,
      personality_prompt: voiceData.personality_prompt,
      phone_number_ids: phoneNumbers?.results?.length > 0 ? voiceData.numbers.map((number) => number.value) : []
    }
    try {
      const res = await createVoice(payload);
      if (res.status === 200) {
        setIsLoading(false);
        addSuccessToast({ message: 'Voice created successfully' });
        trackVoiceCreated({
          user_id: user?.id,
          email: user?.email,
          voice_id: res.data.id,
          voice_name: res.data.name,
          voice_provider: res.data.provider
        });
        // Navigate with state to indicate we just created a voice
        navigate('/voice', { state: { fromVoiceCreation: true, id: res.data.id } });
      } else if (res.status === 403) {
        throw res;
      }
    } catch (err) {
      setIsLoading(false);
      addErrorToast({ message: err.response?.data?.detail || 'Error creating voice' });
    }
  };

  const getPersonalityPromptFromChatbot = async () => {
    if(!voiceData.selected_chatbot) {
      addErrorToast({ message: 'Please select a memory source first.' });
      return;
    }

    try {
      const res = await getChatbotCustomizationById({ kb_id: voiceData.selected_chatbot.value });
      if(res.status === 200) {
        setVoiceData({ ...voiceData, personality_prompt: res.data.base_system_prompt });
      }
    } catch (err) {
      console.error('Error fetching personality prompt from chatbot:', err);
    }
  }

  // Initialize the progress bar steps when the component mounts
  useEffect(() => {
    setLayoutTitle('Create AI Voice Agent');

    // Filter voiceSteps to only include the steps that are part of our form flow
    const filteredSteps = voiceSteps.filter(step => {
      // Don't include phone_numbers step in progress bar if there are no phone numbers
      if (step.id === stepEnumMap['phone_numbers'] && (!phoneNumbers?.results || phoneNumbers.results.length === 0)) {
        return false;
      }
      return Object.values(stepEnumMap).includes(step.id);
    });

    // Make sure the first step is active
    const initialSteps = filteredSteps.map((step, index) => ({
      ...step,
      active: index === 0,
      completed: false,
      pending: index !== 0
    }));

    // Initialize with filtered steps
    setProgressBar(initialSteps);
  }, [phoneNumbers]);

  // Add keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Skip handling if any form element has focus - this prevents interference with typing
      if (
        event.target.tagName === 'INPUT' || 
        event.target.tagName === 'TEXTAREA' || 
                event.target.isContentEditable
      ) {
        // Only handle Enter key for inputs to allow form submission
        if (event.key === 'Enter') {
          if (isCurrentStepValid()) {
            event.preventDefault();
            if (currentStepIndex === formSteps.length - 1) {
              // On the last step, create the voice
              handleCreateVoice();
            } else {
              // Otherwise go to the next step
              goToNextStep();
            }
          }
        }
        return;
      }

      // Handle keyboard navigation
      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          if (isCurrentStepValid()) {
            if (currentStepIndex === formSteps.length - 1) {
// On the last step, create the voice
              handleCreateVoice();
            } else {
// Otherwise go to the next step
              goToNextStep();
            }
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (currentStepIndex === 0) {
// On the first step, show confirmation dialog
            setConfirmExit(true);
          } else {
// Otherwise go to the previous step
            goToPreviousStep();
          }
          break;
        default:
          break;
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Clean up
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentStepIndex, isCurrentStepValid, formSteps.length, goToNextStep, goToPreviousStep, handleCreateVoice, setConfirmExit]);

  useEffect(() => {
    const getVoiceOptions = async () => {
      try {
        const res = await getAllVoiceSettings();
        if(res.status === 200) {
          setVoiceSettings({
            mode: res.data.modes,
            ...res.data.results
          });
        }
      } catch (err) {
        console.error('Error fetching voice settings:', err);
      }
    };
    getVoiceOptions();
  }, []);

  // Fetch all voice providers without filtering
  useEffect(() => {
    const getTextToVoice = async () => {
      try {
        // Call the API without specifying a provider to get all voices
        const res = await getTextToVoiceOptions();
        if(res.status === 200) {
          // Process the voices and add provider information
          const processedVoices = res.data.results.map((voice) => {
            // Determine provider from the voice_type field
            let provider = 'Other';

            // Map voice_type to provider name with proper capitalization
            if (voice.voice_type) {
              switch(voice.voice_type.toLowerCase()) {
                case 'openai':
                  provider = 'OpenAI';
                  break;
                case 'elevenlabs':
                  provider = 'ElevenLabs';
                  break;
                case 'cartesia':
                  provider = 'Cartesia';
                  break;
                default:
                  provider = 'Other';
              }
            }

            return {
              label: voice.name,
              value: voice.id, // Database ID - used for selection and tab switching
              voice_value: voice.id, // The value to use for the voice preview
              soundUrl: voice.preview_url,
              provider: provider,
              description: voice.description || '',
              voice_type: voice.voice_type // Store the original voice_type for reference
            };
          });

          setProviderVoices(processedVoices);
        }
      } catch (err) {
        console.error('Error fetching provider voices:', err);
        setProviderVoices([]);
      }
    };

    // Call the function to fetch all voices when the component mounts
    getTextToVoice();

    // Reset voice selection when voice provider changes
    if (voiceData.voice_provider) {
      setVoiceData((prev) => ({ ...prev, voice: null }));
    }
  }, []);

  // Clear errors when user fills in a field
  useEffect(() => {
    if (voiceData.name.trim() && errors.name) {
      setErrors(prev => ({ ...prev, name: '' }));
    }
  }, [voiceData.name]);

  useEffect(() => {
    if (voiceData.selected_chatbot && errors.selected_chatbot) {
      setErrors(prev => ({ ...prev, selected_chatbot: '' }));
    }
  }, [voiceData.selected_chatbot]);

  useEffect(() => {
    if (voiceData.mode && errors.mode) {
      setErrors(prev => ({ ...prev, mode: '' }));
    }
  }, [voiceData.mode]);

  useEffect(() => {
    if (voiceData.voice_to_text && errors.voice_to_text) {
      setErrors(prev => ({ ...prev, voice_to_text: '' }));
    }
  }, [voiceData.voice_to_text]);

  useEffect(() => {
    if (voiceData.llm && errors.llm) {
      setErrors(prev => ({ ...prev, llm: '' }));
    }
  }, [voiceData.llm]);

  useEffect(() => {
    if (voiceData.voice_provider && errors.voice_provider) {
      setErrors(prev => ({ ...prev, voice_provider: '' }));
    }
  }, [voiceData.voice_provider]);

  useEffect(() => {
    if (voiceData.voice && errors.voice) {
      setErrors(prev => ({ ...prev, voice: null }));
    }
  }, [voiceData.voice]);

  // Reset auto-collapse flag when it's been processed
  useEffect(() => {
    if (autoCollapseVoices) {
      // Reset after a short delay to ensure it's processed
      const timer = setTimeout(() => {
        setAutoCollapseVoices(false);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [autoCollapseVoices]);

  // Replace glow animation with active title styles
  const activeStepStyles = `
    .active-step-title {
      color: #8B5CF6; /* Purple-200 color */
      font-weight: 600;
      position: relative;
    }
    .active-step-title::after {
      content: '';
      position: absolute;
      left: -8px;
      top: 0;
      height: 100%;
      width: 3px;
      background-color: #8B5CF6;
      border-radius: 2px;
    }
    .inactive-step-title {
      color: #27272A; /* Default text color */
      font-weight: 500;
    }
  `;

  // Function to render each form step
  const renderFormStep = (stepIndex) => {
    const currentField = formSteps[stepIndex];
    const isActive = currentStepIndex === stepIndex;
    const hasBeenVisited = isStepVisited(stepIndex);

    // Only render steps that have been visited
    if (!hasBeenVisited) return null;

    // Create anchor point for scrolling
    const anchorPoint = <div ref={el => inputRefs.current[currentField] = el} className="anchor-point"></div>;

    switch (currentField) {
      case 'name':
        return (
          <DTransition
            show={true}
            className={`transition-all duration-300`}
          >
            <div className="transition-all duration-300">
              {anchorPoint}
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <div className="flex items-center gap-size1">
                    <p className={`text-base tracking-tight ${isActive ? 'active-step-title' : 'inactive-step-title'}`}>
                      AI Voice Agent Name
                    </p>
                    <DTooltip content="Set a unique name to easily recognize this AI Voice Agent. This is private and will not be visible anywhere outside of your Dante AI dashboard.">
                      <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                    </DTooltip>
                  </div>
                </div>
                <DInput
                value={voiceData.name}
                  onChange={(e) => {
                    setVoiceData({ ...voiceData, name: e.target.value });
                  updateProgressBar('name');
                  }}
                  placeholder="Enter a name for your AI Voice Agent"
                  error={errors.name}
                />
              </div>
            </div>
          </DTransition>
        );

      case 'selected_chatbot':
        return (
          <DTransition
            show={true}
            className={`transition-all duration-300`}
          >
            <div className="transition-all duration-300">
              {anchorPoint}
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <div className="flex items-center gap-size1">
                    <p className={`text-base tracking-tight ${isActive ? 'active-step-title' : 'inactive-step-title'}`}>
                      Memory of AI Voice Agent
                    </p>
                    <DTooltip content="Select an AI Chatbot's knowledge base as the memory source for this AI Voice Agent.">
                      <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                    </DTooltip>
                  </div>
                </div>
                <DSelect
                  options={
                    chatbotData?.results?.length > 0 
                      ? [
                        ...chatbotData?.results?.map((chatbot) => ({
                          label: chatbot.knowledge_base.knowledge_base_name,
                          value: chatbot.knowledge_base.id
                        })),
                        // Always include create new option
                        { 
                          label: (
                            <div className="flex items-center gap-size0 text-grey-75">
                              <AddIcon className="w-3.5 h-3.5" />
                              <span>Create New Memory Source</span>
                            </div>
                          ),
                          value: "create_new",
                          isCustom: true
                        }
                      ]
                      : [{ // Add create new chatbot option when no chatbots exist
                          label: (
                            <div className="flex items-center gap-size0 text-grey-75">
                              <AddIcon className="w-3.5 h-3.5" />
                              <span>Create New Memory Source</span>
                            </div>
                          ),
                          value: "create_new",
                          isCustom: true
                        }]
                  }
                  selectedChild={
                    voiceData.selected_chatbot?.label || 'Select a memory source'
                  }
                  value={voiceData.selected_chatbot}
                  onChange={(value) => {
                    if (value === "create_new") {
                      // Navigate to create chatbot page
                      handleCreateChatbot();
                      return;
                    }
                    
                    setVoiceData({ ...voiceData, selected_chatbot: {
                      label: chatbotData?.results?.find((c) => c.knowledge_base.id === value)?.knowledge_base.knowledge_base_name,
                      value: value
                    } });
                  }}
                  error={errors.selected_chatbot}
                />
                
                {/* Show helper message only when there are no chatbots */}
                {!chatbotData?.results?.length && (
                  <p className="text-xs text-grey-75 mt-1">
                    You need to create a memory source (AI Chatbot) first to use with your Voice Agent.
                  </p>
                )}
              </div>
            </div>
          </DTransition>
        );

      case 'phone_numbers':
        return phoneNumbers?.results?.length > 0 ? (
          <DTransition
            show={true}
            className={`transition-all duration-300`}
          >
            <div className="transition-all duration-300">
              {anchorPoint}
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <div className="flex items-center gap-size1">
                    <p className={`text-base tracking-tight ${isActive ? 'active-step-title' : 'inactive-step-title'}`}>
                      Phone numbers
                    </p>
                    <DTooltip content="Add one or multiple verified phone numbers you would like this AI Voice Agent to be connected to.">
                      <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                    </DTooltip>
                  </div>
                </div>
                <DMultiselect
                  options={phoneNumbers?.results?.map((item) => ({
                    label: item.number,
                    value: item.id
                  }))}
                  selected={voiceData.numbers}
                  skipSelectAllCheck
                  setSelected={(value) => {
                    setVoiceData({ ...voiceData, numbers: value });
                  }}
                  error={errors.phone_numbers}
                />
              </div>
            </div>
          </DTransition>
        ) : null;

      case 'voice':
        return (
          <DTransition
            show={true}
            className={`transition-all duration-300`}
          >
            <div className="transition-all duration-300">
              {anchorPoint}
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <div className="flex items-center gap-size1">
                    <p className={`text-base tracking-tight ${isActive ? 'active-step-title' : 'inactive-step-title'}`}>
                      AI Voice
                    </p>
                    <DTooltip content="Select the AI Voice you would like this AI Voice Agent to use for delivering responses.">
                      <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                    </DTooltip>
                  </div>
                </div>
                <VoiceSelector
                  voices={providerVoices}
                  selectedVoice={voiceData.voice?.value}
                  autoCollapseOnStep={autoCollapseVoices}
                  onVoiceChange={(value) => {
                    const selectedVoice = providerVoices.find((opt) => opt.value === value);
                    setVoiceData({
                      ...voiceData,
                      voice: {
                        label: selectedVoice?.label,
                        value,
                        voice_value: selectedVoice?.voice_value
                      }
                    });
                  }}
                  disabled={isLoading}
                  error={errors.voice ? 'AI Voice selection is required.' : null}
                />
              </div>
            </div>
          </DTransition>
        );

      case 'welcome_message':
        return (
          <DTransition
            show={true}
            className={`transition-all duration-300`}
          >
            <div className="transition-all duration-300">
              {anchorPoint}
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <div className="flex items-center gap-size1">
                    <p className={`text-base tracking-tight ${isActive ? 'active-step-title' : 'inactive-step-title'}`}>
                      Initial Greeting Message
                    </p>
                    <DTooltip content="Set the first message your AI Voice Agent will say when answering a call, e.g. &quot;Hello, thank you for calling Dante AI. What can I help you with today?&quot;">
                      <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                    </DTooltip>
                  </div>
                </div>
                <DInput
                  value={voiceData.welcome_message}
                  onChange={(e) => {
                    setVoiceData({ ...voiceData, welcome_message: e.target.value });
                  }}
                  placeholder="Write your greeting message"
                />
              </div>
            </div>
          </DTransition>
        );

      case 'personality_prompt':
        return (
          <DTransition
            show={true}
            className={`transition-all duration-300`}
          >
            <div className="transition-all duration-300">
              {anchorPoint}
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <div className="flex items-center gap-size1">
                    <p className={`text-base tracking-tight ${isActive ? 'active-step-title' : 'inactive-step-title'}`}>
                      Conversation Style Instructions
                    </p>
                    <DTooltip content="Define how your AI Voice Agent should interact during conversations. You can use the existing style from your memory source or create custom instructions.">
                      <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                    </DTooltip>
                  </div>
                </div>
                <DTextArea
                  value={voiceData.personality_prompt}
                  onChange={(e) => {
                    setVoiceData({ ...voiceData, personality_prompt: e.target.value });
                  }}
                  placeholder="Enter conversation style instructions"
                  autoExpand
                  maxHeight="300px"
                  minRows={3}
                />
              </div>
            </div>
          </DTransition>
        );

      default:
        return null;
    }
  };

  // Check voice limit against tier limitation
  useEffect(() => {
    const checkVoiceLimits = async () => {
      try {
        const res = await getVoices();
        if (res.status === 200) {
          // Get the allowed voice count from tier feature check
          const allowedVoiceCount = getNumericalFeatureValue('nr_ai_voice_agents');

          const voicesCreated = res.data.results?.length || 0;
          if (voicesCreated >= allowedVoiceCount) {
            setExceedsVoiceLimit(true);
            openInlinePlansModal('nr_ai_voice_agents');
          } else {
            setExceedsVoiceLimit(false);
          }
        }
      } catch (err) {
        console.error('Error checking voice limits:', err);
      }
    };
    
    checkVoiceLimits();
  }, [ ]);

  // Effect to handle selection of newly created chatbot when returning from chatbot creation
  useEffect(() => {
    // If we have a newly created chatbot ID and chatbot data is loaded
    if (newlyCreatedChatbotId && chatbotData?.results?.length > 0) {
      // Find the chatbot with the matching ID
      const newChatbot = chatbotData.results.find(
        (chatbot) => chatbot.knowledge_base.id === newlyCreatedChatbotId
      );
      
      if (newChatbot) {
        // Select the newly created chatbot
        setVoiceData((prev) => ({
          ...prev,
          selected_chatbot: {
            label: newChatbot.knowledge_base.knowledge_base_name,
            value: newChatbot.knowledge_base.id
          }
        }));
        
        // If we're on the chatbot selection step, advance to the next step
        if (currentStepIndex === formSteps.indexOf('selected_chatbot')) {
          goToNextStep();
        }
      }
    }
  }, [newlyCreatedChatbotId, chatbotData, currentStepIndex, formSteps, goToNextStep]);

  // Handle navigation to create chatbot page
  const handleCreateChatbot = () => {
    // Navigate to chatbot creation page, with a flag to return to voice creation afterward
    navigate('/chatbot/create', { 
      state: { 
        returnToVoiceCreation: true, 
        defaultVoiceName: voiceData.name 
      } 
    });
  };

  return (
    <div className="flex flex-col md:flex-row gap-size3 h-full relative">
      {/* Add style tag for active step title styling */}
      <style>{activeStepStyles}</style>
      
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <VoicePreview
              chatbotId={voiceData.selected_chatbot?.value}
              voiceId={voiceData.voice?.value}
              welcomeMessage={voiceData.welcome_message}
              phoneNumbers={phoneNumbers?.results?.length > 0 ? selectedNumbers.map(num => num.label) : []}
              personalityPrompt={voiceData.personality_prompt}
              hideCloseButton={true}
            />
          </div>
        )}
      >
        {(handleRightSidebar) => (
          <LayoutWithButtons
            footer={
              !isLoading && (
                <div className="layout_buttons flex justify-between">
                  {currentStepIndex === formSteps.length - 1 && (
                    <DAlert state="alert" className="!w-full items-center">
                      <p className="text-sm font-regular tracking-tight mt-[2px]">
                        Please review all the details added. Once you're happy with the set up, click 'Create' to build your AI Voice Agent.
                      </p>
                    </DAlert>
                  )}
                </div>
              )
            }
          >
            {!isLoading && (
              <div className="flex flex-col gap-size5 max-w-[650px] mx-auto w-full ">
                <InlineGlobalModals />
                <div className="flex gap-size2 justify-end">
                  {currentStepIndex === formSteps.length - 1 ? (
                    <DButton
                      variant="contained"
                      size="sm"
                      onClick={handleCreateVoice}
                      disabled={!isCurrentStepValid() || exceedsVoiceLimit}
                      className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3"
                    >
                      Create
                    </DButton>
                  ) : (
                    <DButton
                      variant="contained"
                      size="sm"
                      onClick={goToNextStep}
                      disabled={!isCurrentStepValid() || exceedsVoiceLimit}
                      className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3"
                    >
                      Next
                    </DButton>
                  )}
                </div>

                <div className="flex flex-col gap-size0 mb-size0">
                  <p className="text-sm text-grey-75 font-light">Read our <a className="text-purple-300 underline font-medium" href="https://www.dante-ai.com/guides/how-to-create-an-ai-voice-agent" target="_blank" rel="noopener noreferrer">step-by-step guide</a> on how to create an AI Voice Agent.</p>
                </div>

                {/* Dynamic form steps */}
                <div className="flex flex-col gap-size5 overflow-y-auto max-h-[calc(100vh-350px)] no-scrollbar">
                  {formSteps.map((_, index) => renderFormStep(index))}
                </div>
              </div>
            )}

            {isLoading && (
              <div className="flex flex-col justify-center items-center h-full gap-size1">
                <DLoaderTraining />
                <p className="text-xl font-regular tracking-tight">Creating AI Voice Agent...</p>
              </div>
            )}
          </LayoutWithButtons>
        )}
      </LayoutRightSidebar>

      <DConfirmationModal
        open={confirmExit}
        onClose={() => setConfirmExit(false)}
        onConfirm={() => {
          navigate('/voice');
        }}
        title="Leave creation?"
        description="Progress you made so far won't be saved."
        confirmText="Leave"
        cancelText="Cancel"
        variantConfirm="danger"
      />
    </div>
  );
};

export default CreateVoice;
