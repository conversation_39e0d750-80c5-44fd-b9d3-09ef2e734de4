import { useEffect, useState } from "react";
import Chat<PERSON>anager from "@/components/Chatbot/ChatManager";
import CategoryIcon from "@/components/Global/CategoryIcon";
import ChevronDownIcon from "@/components/Global/Icons/ChevronDownIcon";
import ConfirmIcon from "@/components/Global/Icons/ConfirmIcon";
import CopyIcon from "@/components/Global/Icons/CopyIcon";
import FlagIcon from "@/components/Global/Icons/FlagIcon";
import useDanteApi from "@/hooks/useDanteApi";
import { getChatbotCustomizationById } from "@/services/customization.service";
import { Disclosure, DisclosureButton, DisclosurePanel, Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { getConversationById } from "@/services/humanHandover.js";
import { useParams } from "react-router-dom";
import { useUserStore } from "@/stores/user/userStore";
import MenuCategory from "@/components/Chatbot/MenuCategory";
import * as humanHandoverService from '@/services/humanHandover';
import DLoading from "@/components/DLoading";
import DButton from "@/components/Global/DButton";
import { useHumanHandoverStore } from "@/stores/humanHandover/humanHandoverStore";

const HumanHandoverConversation = () => {
    const { auth } = useUserStore();
    const { conversation_id, organization_id } = useParams();
    const { openedConversation } = useHumanHandoverStore((state) => state);
    const { data , isLoading } = useDanteApi(getConversationById ,[openedConversation], {}, {
        conversation_id: conversation_id
    })

    const {
        data: conversationData,
        isLoading: isConversationDataLoading,
      } = useDanteApi(
        humanHandoverService.getConversationsDashboardData,
        [],
        {},
        organization_id,
      );

    const [chatData, setChatData] = useState(null)
    const [categoryId, setCategoryId] = useState(null)

    const handleCopy = (value) => {
        navigator.clipboard.writeText(value);
    }

    const handleCategoryClick = async (hh_category_id) => {
        try {
          const response = await humanHandoverService.updateCategoryConversation(
            conversation_id,
            hh_category_id
          );
          if (response.status === 200) {
            setCategoryId(hh_category_id)
          }
        } catch (error) {
          console.error(error);
        }
    };

    const handleMarkAsResolved = async () => {
        try {
            const response = await humanHandoverService.markResolved(conversation_id);
            if (response.status === 200) {
                // Update the conversation state
                window.location.href = `/human-handover/${organization_id}/conversation/${conversation_id}`;
            }
        } catch (error) {
            console.error(error);
        }
    }

    const fetchCustomizationData = async () => {
        if (data) {
            setCategoryId(data?.category_id);
            const { data: customizationData } =
                await getChatbotCustomizationById({
                    kb_id: data.kb_id,
                });

            setChatData(customizationData);
        }
    };

    useEffect(() => {
        fetchCustomizationData();
    }, [data]);

    if (isLoading || isConversationDataLoading) {
        return <DLoading />
    }


    return (
        <div className="bg-white h-[1px] grow p-size5 md:p-size5 flex flex-col gap-size4 md:gap-size5 overflow-y-auto">
            <div className="flex gap-size5 h-full">
                <div className="rounded-size1 p-size3 flex-col gap-size3 border border-grey-5 min-w-60 xl:min-w-80 hidden md:flex overflow-y-auto no-scrollbar">
                    <p className="text-xl font-medium tracking-tight">Requests</p>
                    <Disclosure>
                        <DisclosureButton className="flex items-center justify-between">
                            <span>
                                New
                            </span>
                            <ChevronDownIcon className="w-3 group-data-[open]:rotate-180" />
                        </DisclosureButton>
                        <DisclosurePanel>
                            {conversationData?.conversations
                                .filter(conv => !conv.start_live_agent_date && !conv.resolved_date)
                                .map(conv => (
                                    <div
                                        key={conv.id}
                                        className="flex items-center justify-between mb-size2 cursor-pointer hover:bg-grey-5 p-size1 rounded-size1"
                                        onClick={() => window.location.href = `/human-handover/${organization_id}/conversation/${conv.id}`}
                                    >
                                        <div className="flex flex-col">
                                            <span className="text-xs tracking-tight font-regular">#{conv.request_id.toString().padStart(4, '0')}</span>
                                            <span className="text-xs tracking-tight font-regular text-grey-50">{conv.user_email}</span>
                                        </div>
                                        {conv.is_flagged && (
                                            <div className="flex items-center justify-center border border-orange-300 rounded-full size-7">
                                                <FlagIcon className='text-orange-300 size-4'/>
                                            </div>
                                        )}
                                    </div>
                                ))}
                        </DisclosurePanel>
                    </Disclosure>

                    <div className="w-full h-[1px] bg-grey-5"></div>

                    <Disclosure>
                        <DisclosureButton className="flex items-center justify-between">
                            <span>
                                Taken
                            </span>
                            <ChevronDownIcon className="w-3 group-data-[open]:rotate-180" />
                        </DisclosureButton>
                        <DisclosurePanel>
                            {conversationData?.conversations
                                .filter(conv => conv.start_live_agent_date && !conv.resolved_date)
                                .map(conv => (
                                    <div
                                        key={conv.id}
                                        className="flex items-center justify-between mb-size2 cursor-pointer hover:bg-grey-5 p-size1 rounded-size1"
                                        onClick={() => window.location.href = `/human-handover/${organization_id}/conversation/${conv.id}`}
                                    >
                                        <div className="flex flex-col">
                                            <span className="text-xs tracking-tight font-regular">#{conv.request_id.toString().padStart(4, '0')}</span>
                                            <span className="text-xs tracking-tight font-regular text-grey-50">{conv.user_email}</span>
                                        </div>
                                        {conv.is_flagged && (
                                            <div className="flex items-center justify-center border border-orange-300 rounded-full size-7">
                                                <FlagIcon className='text-orange-300 size-4'/>
                                            </div>
                                        )}
                                    </div>
                                ))}
                        </DisclosurePanel>
                    </Disclosure>

                    <div className="w-full h-[1px] bg-grey-5"></div>

                    <Disclosure>
                        <DisclosureButton className="flex items-center justify-between">
                            <span>
                                Closed
                            </span>
                            <ChevronDownIcon className="w-3 group-data-[open]:rotate-180" />
                        </DisclosureButton>
                        <DisclosurePanel>
                            {conversationData?.conversations
                                .filter(conv => conv.resolved_date)
                                .map(conv => (
                                    <div
                                        key={conv.id}
                                        className="flex items-center justify-between mb-size2 cursor-pointer hover:bg-grey-5 p-size1 rounded-size1"
                                        onClick={() => window.location.href = `/human-handover/${organization_id}/conversation/${conv.id}`}
                                    >
                                        <div className="flex flex-col">
                                            <span className="text-xs tracking-tight font-regular">#{conv.request_id.toString().padStart(4, '0')}</span>
                                            <span className="text-xs tracking-tight font-regular text-grey-50">{conv.user_email}</span>
                                        </div>
                                        <ConfirmIcon className='text-green-300 size-6'/>
                                    </div>
                                ))}
                        </DisclosurePanel>
                    </Disclosure>
                </div>
                <div className="w-full h-full flex flex-col">
                    <div className="py-size3 px-size2 flex items-center justify-between">
                        <p className="text-lg tracking-tight font-regular">Conversation #{data?.request_id.toString().padStart(4, '0')}</p>
                        <div className="flex items-center gap-size1">
                            {/* <Menu>
                                <MenuButton className="py-size1 px-size2 rounded-size1 border border-grey-5 flex items-center gap-size1 text-grey-50 w-36">
                                    <CategoryIcon className="text-grey-50 size-4" />
                                    Not defined
                                </MenuButton>
                                <MenuItems anchor="bottom" className="bg-white rounded-size1 border border-grey-5 p-size1 flex flex-col gap-size2 border-t-0 rounded-t-none w-36">
                                    {categories.map((category, index) => (
                                        <MenuItem key={index} className="py-size0 px-size1 hover:bg-grey-5 rounded-size0">
                                            <div className="flex items-center gap-size1 text-grey-50 text-sm">
                                                <CategoryIcon  style={{color: category.color}} className={`size-4`} />
                                                {category.name}
                                            </div>
                                        </MenuItem>
                                    ))}
                                </MenuItems>
                            </Menu> */}
                            <MenuCategory
                                currentCategoryId={categoryId}
                                handleCategoryClick={handleCategoryClick}
                                view="conversation"
                            />
                            {conversationData?.state === 'pending' && <DButton className="py-size1 px-size2 rounded-size1 border border-green-10 flex bg-green-5 items-center gap-size1 text-green-300"
                                onClick={() => handleMarkAsResolved()}
                            >
                                <ConfirmIcon className="text-green-300 size-4" />
                                Mark as resolved
                            </DButton>}
                        </div>

                    </div>
                    <div className="bg-grey-5 py-size1 px-size2 grid grid-cols-2 md:grid-cols-3 gap-size2">
                        <div>
                            <p className="text-grey-50 text-xs tracking-tight font-regular">Name:</p>
                            <div className="flex items-center gap-sizeo">
                                <p className="text-sm tracking-tight font-regular">{data?.user_name}</p>
                                <button onClick={() => handleCopy(data?.user_name)}>
                                    <CopyIcon className="text-grey-50 size-4"/>
                                </button>
                            </div>
                        </div>
                        <div>
                            <p className="text-grey-50 text-xs tracking-tight font-regular">Email:</p>
                            <div className="flex items-center gap-sizeo">
                                <p className="text-sm tracking-tight font-regular">{data?.user_email}</p>
                                <button onClick={() => handleCopy(data?.user_email)}>
                                    <CopyIcon className="text-grey-50 size-4"/>
                                </button>
                            </div>
                        </div>

                    </div>
                    <div className="grow overflow-y-auto no-scrollbar py-size1">
                        <ChatManager
                            config={{ ...chatData, token: auth.access_token }}
                            initialShouldFetchCustomization={false}
                            isPreviewMode={false}
                            isInApp={true}
                            showInAppHeader={false}
                            isInHumanHandoverApp={true}
                            liveAgentConversation={data}
                            isLiveAgentConversationResolved={data?.state === 'resolved'}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HumanHandoverConversation;