import DDoughnutChart from '@/components/HumanHandovers/Charts/Doughnut';
import DSelect from '@/components/Global/DSelect';
import LayoutMain from '@/layouts/LayoutMain';
import DHorizontalBarChart from '@/components/HumanHandovers/Charts/DHorizontalBar';
import FilterCard from '@/components/FilterCard';
import { useEffect, useState } from 'react';
import { DateTime } from 'luxon';
import useDanteApi from '@/hooks/useDanteApi';
import { useParams } from 'react-router-dom';
import * as humanHandoverService from '@/services/humanHandover';
import formatDuration from '@/helpers/formatDuration';

const filterOptions = [
  {
    label: 'Week to date',
    value: DateTime.now().startOf('week').toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  },
  {
    label: 'Month to date',
    value: DateTime.now()
      .startOf('month')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  },
  {
    label: 'Last 7 days',
    value: DateTime.now()
      .minus({ days: 7 })
      .startOf('day')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  },
  {
    label: 'Last 30 days',
    value: DateTime.now()
      .minus({ days: 30 })
      .startOf('day')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  },
  {
    label: 'Last 90 days',
    value: DateTime.now()
      .minus({ days: 90 })
      .startOf('day')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  },
];

const HumanHandoverInsights = () => {
  const { organization_id } = useParams();

  const [data, setData] = useState({
    total_conversations: 0,
    waiting_requests: 0,
    live_conversations: 0,
    responses_by_you: 0,
    response_rate: 0,
    average_waiting_time: 0,
    average_conversation_duration: 0,
    tickets_by_categories: [
      {
        label: 'Not defined',
        value: 0,
        color: '#09081F80',
      },
      {
        label: 'Bug',
        value: 0,
        color: '#EA3529',
      },
      {
        label: 'Issue',
        value: 0,
        color: '#FF9C28',
      },
      {
        label: 'Support',
        value: 0,
        color: '#33C067',
      },
    ],
    week: [
      {
        label: 'Monday',
        value: 0,
      },
      {
        label: 'Tuesday',
        value: 0,
      },
      {
        label: 'Wednesday',
        value: 0,
      },
      {
        label: 'Thursday',
        value: 0,
      },
      {
        label: 'Friday',
        value: 0,
      },
      {
        label: 'Saturday',
        value: 0,
      },
      {
        label: 'Sunday',
        value: 0,
      },
    ],
  });
  const [period, setPeriod] = useState(filterOptions[0].value);

  const [timeRange, setTimeRange] = useState({
    date_from: DateTime.now()
      .minus({ days: 30 })
      .startOf('day')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    date_to: DateTime.now().toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  });

  const { data: dataInsights } = useDanteApi(
    humanHandoverService.getInsights,
    [timeRange.date_from, timeRange.date_to, organization_id],
    {},
    organization_id,
    timeRange.date_from,
    timeRange.date_to
  );

  useEffect(() => {
    if (dataInsights) {
      setData({
        ...dataInsights,
        week: data.week.map((item) => ({
          label: item.label,
          value: dataInsights.average_tickets_weekly[item.label],
        })),
        tickets_by_categories: dataInsights.categories.map((item) => ({
          ...item,
          label: item.name || '',
          value: dataInsights.tickets_by_category[item.name] || 0,
        })),
      });
    }
  }, [dataInsights]);

  return (
    <div className="bg-white h-full p-size5 flex flex-col gap-size5">
      <header className="flex justify-between items-center">
        <p className="text-2xl font-medium">Insights</p>
        <div className="flex items-center gap-size1 w-40">
          <DSelect
            options={filterOptions}
            value={period}
            onChange={(value) => {
              setTimeRange({ ...timeRange, date_from: value });
              setPeriod(value);
            }}
          />
        </div>
      </header>
      <div className="flex flex-col md:flex-row gap-size1">
        <FilterCard
          number={data.total_conversations}
          label="Total conversations"
          tooltip="Total number of conversations"
          showCheckbox={false}
          className="!bg-purple-5"
        />

        <FilterCard
          number={data.waiting_requests}
          label="Waiting requests"
          tooltip="Conversations that are waiting for a response from the human agent"
          showCheckbox={false}
          className="!bg-purple-5"
        />

        <FilterCard
          number={data.live_conversations}
          label="Live conversations"
          tooltip="Conversations that are currently being handled by the human agent"
          showCheckbox={false}
          className="!bg-purple-5"
        />

        <FilterCard
          number={data.responses_by_you}
          label="Responses by you"
          tooltip="Conversations that are responded by the human agent"
          showCheckbox={false}
          className="!bg-purple-5"
        />
      </div>
      <div className="flex flex-col md:flex-row gap-size0">
        <FilterCard
          number={`${data.response_rate}%`}
          label="Response rate"
          tooltip="Percentage of conversations that are responded by the human agent"
          showCheckbox={false}
        />

        <FilterCard
          number={formatDuration({
            minutes: data.average_waiting_time,
          })}
          label="Average waiting time"
          tooltip="Average time that the human agent takes to respond to a conversation"
          showCheckbox={false}
        />

        <FilterCard
          number={formatDuration({
            minutes: data.average_conversation_duration,
          })}
          label="Average conversation duration"
          tooltip="Average time that conversations take to be resolved"
          showCheckbox={false}
        />
      </div>
      <div className="flex flex-col md:flex-row gap-size2 w-full">
        <div className="flex flex-col rounded-size2 border p-size5 md:w-1/2 gap-size5">
          <h2 className="text-xl font-medium tracking-tight border-b border-grey-10 pb-size3">
            Conversations by category
          </h2>
          <div className="flex flex-col gap-size0 justify-center items-center">
            <div className="relative w-full h-full max-w-[360px] ">
              <div className="absolute w-9/12 md:w-10/12 aspect-square left-1/2 -translate-x-1/2  text-3xl font-bold text-grey-30 flex justify-center items-center">
                {data.total_conversations}
              </div>
              <DDoughnutChart data={data.tickets_by_categories} />
            </div>
          </div>
        </div>

        <div className="flex flex-col rounded-size2 border p-size5 md:w-1/2 gap-size5">
          <h2 className="text-xl font-medium tracking-tight border-b border-grey-10 pb-size3">
            Average number of conversations weekly
          </h2>
          <div className="flex flex-col gap-size0">
            <DHorizontalBarChart options={{}} data={data.week} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HumanHandoverInsights;
