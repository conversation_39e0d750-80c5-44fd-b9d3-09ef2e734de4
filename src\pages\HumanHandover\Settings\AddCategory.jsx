import { useState } from "react";
import DButton from "@/components/Global/DButton";
import DColorInput from "@/components/Global/DColorInput";
import DInput from "@/components/Global/DInput/DInput";
import DInputBlock from "@/components/Global/DInput/DInputBlock";
import DModal from "@/components/Global/DModal";

const AddCategory = ({ open, onClose, handleAddCategory }) => {
    const [categoryName, setCategoryName] = useState('');
    const [categoryColor, setCategoryColor] = useState('#8275F7');
    const [categoryDescription, setCategoryDescription] = useState('');

    return (
        <DModal 
            isOpen={open} 
            onClose={onClose}
            title="Add Category"
            footer={
                <div className="flex items-center gap-size2 w-full">
                    <DButton variant="grey" onClick={onClose} fullWidth>
                        Cancel
                    </DButton>
                    <DButton variant="dark" onClick={() => handleAddCategory({ name: categoryName, color: categoryColor, description: categoryDescription })} fullWidth>
                        Add
                    </DButton>
                </div>
            }
        >
            <DInputBlock label="Name">
                <DInput
                    name="name"
                    placeholder="Enter category name"
                    value={categoryName}
                    onChange={(e) => setCategoryName(e.target.value)}
                />
            </DInputBlock>
            <DInputBlock label="Description">
                <DInput
                    name="description"
                    placeholder="Enter category description"
                    value={categoryDescription}
                    onChange={(e) => setCategoryDescription(e.target.value)}
                />
            </DInputBlock>
            <DInputBlock label="Label Color">
                <DColorInput
                    value={categoryColor}
                    onChange={(color) => setCategoryColor(color.hex)}
                />
            </DInputBlock>
        </DModal>
    )
}

export default AddCategory;