import DLoading from "@/components/DLoading";
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { acceptTeamManagementInvitation } from "@/services/teamManagement.service";
import useToast from "@/hooks/useToast";

const AcceptTeamInvite = () => {
    const [urlParams] = useSearchParams();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const {addSuccessToast} = useToast()
    
    useEffect(() => {
        const accept = async () => {
        try {
            setLoading(true);
            const response = await acceptTeamManagementInvitation(
            urlParams.get('team_id'),
            urlParams.get('token')
            );
            if (response.status === 200) {
            addSuccessToast({ message: 'Your invitation has been accepted.' });
            setLoading(false);
            navigate('/log-in');
            }
        } catch (error) {
            console.error(error);
            setLoading(false);
            navigate('/log-in');
        }
        };
        accept();
    }, []);

  return <DLoading show={loading} />;
};

export default AcceptTeamInvite;
