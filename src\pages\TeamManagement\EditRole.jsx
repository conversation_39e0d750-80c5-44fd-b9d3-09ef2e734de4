import DButton from "@/components/Global/DButton";
import DCheckbox from "@/components/Global/DCheckbox";
import DInput from "@/components/Global/DInput/DInput";
import DModal from "@/components/Global/DModal";
import DTooltip from "@/components/Global/DTooltip";
import InfoIcon from "@/components/Global/Icons/InfoIcon";
import { useEffect } from "react";

const EditRole = ({ 
    open, 
    onClose, 
    onSubmit, 
    role, 
    error, 
    setError, 
    selectedPermissions, 
    setSelectedPermissions,
    changedValuesRolePermissions,
    setChangedValuesRolePermissions,
    selectedRole,
    setSelectedRole
}) => {

    useEffect(() => {
        setSelectedPermissions(role?.permissions.filter((permission) => permission.value).map((permission) => permission.name));
    }, [role]);

    const addPermissions = (name, value) => {
        if(selectedPermissions.includes(name)) {
            setSelectedPermissions(selectedPermissions.filter((permission) => permission !== name));
        } else {
            setSelectedPermissions([...selectedPermissions, name]);
        }
        setChangedValuesRolePermissions({
            ...changedValuesRolePermissions,  
            [name]: value 
        });
    };
  return (
    <DModal 
        title="Edit Role"
        isOpen={open}
        onClose={onClose}
        className='!min-w-[550px]'
        footer={
          <div className="flex items-center gap-size1 w-full">
            <DButton variant="outlined" onClick={onClose} fullWidth>Cancel</DButton>
            <DButton onClick={onSubmit} fullWidth variant="dark">Save</DButton>
          </div>
        }
    >
    <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Role name</p>
          <DInput
            placeholder="Enter role name"
            value={selectedRole?.name}
            onChange={(e) => {
              setChangedValuesRolePermissions({
                ...changedValuesRolePermissions,
                name: e.target.value
              });
              setSelectedRole({
                ...selectedRole,
                name: e.target.value
              });
            }}
            error={error.roleName}
          />
        </div>
        <div className="flex flex-col gap-size1">
          <div className="flex flex-col gap-size0">
            <p className="text-base font-medium tracking-tight">Set role permissions</p>
            <p className="text-grey-50 text-xs font-light tracking-tight">
              Start by selecting a Primary permission
            </p>
          </div>
          <div className='flex flex-col gap-size1 max-h-[300px] overflow-y-auto no-scrollbar'>
          {role?.permissions.map((permission, index) => {
              const isSelected = selectedPermissions?.includes(permission.name);

              return (
                  <div className="flex items-center py-size0 gap-size1" key={index}>
                  <DCheckbox
                    key={permission.name}
                    checked={isSelected}
                    label={permission.label}
                    style={{width: 'fit-content'}}
                    onChange={(value) => addPermissions(permission.name, value)}
                    hideError
                  />
                  <DTooltip content={permission.help_text}>
                    <InfoIcon className="size-3 text-grey-50" />
                  </DTooltip>
                </div>
              )
            })}
          </div>
        </div>
      </div>
      
    </DModal>
  )
}

export default EditRole;