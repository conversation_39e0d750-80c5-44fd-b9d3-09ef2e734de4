import { useEffect, useState } from "react";
import ChatListMessages from "@/components/Chatbot/ChatListMessages";
import DButton from "@/components/Global/DButton";
import DButtonIcon from "@/components/Global/DButtonIcon";
import DTextArea from "@/components/Global/DInput/DTextArea";
import DSwitch from "@/components/Global/DSwitch";
import SendIcon from "@/components/Global/Icons/SendIcon";
import useDanteA<PERSON> from "@/hooks/useDanteApi";
import { getVoiceConversationDetails } from "@/services/voice.service";
import { useParams } from "react-router-dom";
import { DANTE_ICON } from "@/constants";
import ChevronLeftIcon from "@/components/Global/Icons/ChevronLeftIcon";
import { useNavigate } from "react-router-dom";

const VoiceConversationDetails = () => {
    const navigate = useNavigate();
    const params = useParams();
    const { data: conversationDetails, refetch } = useDanteApi(getVoiceConversationDetails, [], { }, params.conversationId);
    const [isLiveConversationRecordingEnabled, setIsLiveConversationRecordingEnabled] = useState(true);
    
    //if live conversation recording is enabled, referch data every 2 seconds
    useEffect(() => {
        if(isLiveConversationRecordingEnabled) {
            const interval = setInterval(() => {
                refetch();
            }, 2000);
            return () => clearInterval(interval);
        }
    }, [isLiveConversationRecordingEnabled]);

    return (
        <div className="flex flex-col gap-size5 bg-white rounded-size1 p-size5 h-full w-full">
            <div className="flex justify-between items-center">
                <div className="flex items-start gap-size2">
                    <DButtonIcon variant="outlined" onClick={() => navigate(-1)}>
                        <ChevronLeftIcon />
                    </DButtonIcon>
                    <div className="flex flex-col gap-size1">
                        <h2 className="text-xl font-medium">Voice Conversation Details</h2>
                        <p className="text-base text-grey-75">Phone Number: {conversationDetails?.caller_number}</p>
                    </div>
                </div>
                <DSwitch
                    label="Live conversation updates"
                    checked={isLiveConversationRecordingEnabled}
                    onChange={(checked) => setIsLiveConversationRecordingEnabled(checked)}
                />
            </div>
            <div className="flex gap-size3 w-full flex-grow">
                <div className="flex flex-col gap-size2 border rounded-size1 p-size2 rounded-size1 w-full h-full">
                    <div 
                        className="overflow-y-auto no-scrollbar flex-grow h-0 flex flex-col gap-size3"
                        ref={(el) => {
                            if (el && isLiveConversationRecordingEnabled) {
                                el.scrollTop = el.scrollHeight;
                            }
                        }}
                    >
                        {conversationDetails?.messages
                            .sort((a, b) => new Date(a.date_created) - new Date(b.date_created))
                            .map(message => (
                            <div key={message.id} className={`flex gap-size5 ${message.role === 'user' ? 'justify-end ml-auto' : 'justify-start'}`}>
                                {message.role === 'assistant' && (
                                    <div
                                        className="rounded-full size-6 flex items-center justify-center overflow-hidden shrink-0"
                                        style={{ backgroundColor: `var(--dt-color-brand-10)` }}
                                    >
                                        <img src={DANTE_ICON} className="size-4 object-contain" alt="" />
                                    </div>
                                )}
                                <div 
                                       style={{
                                        color: `var(--dt-color-element-100)`,
                                        borderColor: `var(--dt-color-element-5)`,
                                        backgroundColor:
                                            message.role === 'user'
                                                ? `var(--dt-color-element-2)`
                                                : 'transparent',
                                      }}
                                    className={`${message.role === 'user' ? 'rounded-size2 space-y-size3 w-fits border px-5 py-2.5 whitespace-pre-wrap' : 'bg-white text-black'}`}>
                                    <p>{message.content}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default VoiceConversationDetails;

{/* <div className="flex flex-col gap-size2 border rounded-size1 p-size2 rounded-size1 w-full">
    <p className="text-base font-medium">Send a message to the active call</p>
    <p className="text-sm text-grey-50">Use this to send a message to the AI during an active call. This allows you to guide the conversation or provide specific topics for the AI voice to discuss with the caller.</p>
    <div className="relative">
        <textarea className="w-full h-full resize-none border rounded-size1 p-size2 pr-[40px] rounded-size1" />
        <DButtonIcon
            className="send-button absolute bottom-2 right-2"
            onClick={() => handleSend()}
            style={{
            color: `var(--dt-color-element-100)`,
            backgroundColor: `var(--dt-color-surface-100)`,
            }}
        >
            <SendIcon />
        </DButtonIcon>
    </div>
</div> */}