import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import LayoutMain from "@/layouts/LayoutMain";
import DLoading from "@/components/DLoading";
import useToast from "@/hooks/useToast";
import { cancelNumberPurchase } from "@/services/phoneNumberPurchase.service";
import CancelIcon from "@/components/Global/Icons/CloseIcon";

const PhoneNumberPurchaseCancel = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { addSuccessToast, addErrorToast } = useToast();

    const [isLoading, setIsLoading] = useState(true);
    const [countdown, setCountdown] = useState(5);

    useEffect(() => {
        // Use sessionStorage to track if the purchase has already been cancelled
        const hasCancelledPurchase = sessionStorage.getItem('phone_number_purchase_cancelled');

        const params = new URLSearchParams(location.search);
        const token = params.get('token');
        const userId = params.get('user_id');

        if (!token || !userId) {
            addErrorToast({ message: "Invalid parameters. Redirecting to phone numbers page." });
            setTimeout(() => {
                navigate('/phone-numbers');
            }, 3000);
            return;
        }

        const cancelPurchase = async () => {
            // Only proceed if we haven't already cancelled this purchase
            if (hasCancelledPurchase === token) {
                setIsLoading(false);
                return;
            }

            try {
                await cancelNumberPurchase(token, userId);
                addSuccessToast({ message: "Purchase cancelled successfully." });
                // Mark this purchase as cancelled
                sessionStorage.setItem('phone_number_purchase_cancelled', token);
            } catch (error) {
                console.error("Error cancelling purchase:", error);
                // We don't show an error toast here as it's not critical
            } finally {
                setIsLoading(false);
            }
        };

        cancelPurchase();
    }, [location.search, navigate, addSuccessToast, addErrorToast]);

    useEffect(() => {
        if (!isLoading) {
            const timer = setInterval(() => {
                setCountdown((prev) => {
                    if (prev <= 1) {
                        clearInterval(timer);
                        navigate('/phone-numbers');
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);

            return () => clearInterval(timer);
        }
    }, [isLoading, navigate]);

    return (
        <LayoutMain title="Purchase Cancelled">
            {isLoading ? (
                <DLoading show={true} />
            ) : (
                <div className="flex flex-col items-center justify-center py-size10">
                    <div className="bg-white p-size5 rounded-size1 shadow-sm max-w-md w-full flex flex-col items-center">
                        <div className="w-16 h-16 bg-grey-10 rounded-full flex items-center justify-center mb-size3">
                            <CancelIcon className="w-8 h-8 text-grey-50" />
                        </div>

                        <h2 className="text-2xl font-medium mb-size2">Purchase Cancelled</h2>

                        <p className="text-grey-75 text-center mb-size5">
                            Your phone number purchase has been cancelled. No charges have been made to your account.
                        </p>

                        <p className="text-sm text-grey-50">
                            Redirecting to phone numbers page in {countdown} seconds...
                        </p>
                    </div>
                </div>
            )}
        </LayoutMain>
    );
};

export default PhoneNumberPurchaseCancel;
