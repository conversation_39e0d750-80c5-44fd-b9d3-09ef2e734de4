import { useState } from 'react';
import DModal from '@/components/Global/DModal';
import DButton from '@/components/Global/DButton';
import { createNumberPurchaseCheckoutSession } from '@/services/phoneNumberPurchase.service';
import useToast from '@/hooks/useToast';
import DBadge from '@/components/Global/DBadge';
import { STATUS } from '@/constants';
import { trackBuyNumberClick } from '@/helpers/analytics';
import { useUserStore } from '@/stores/user/userStore';

const PhoneNumberPurchaseModal = ({ isOpen, onClose, phoneNumber, phoneData, userData }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { addErrorToast } = useToast();
  const { user } = useUserStore();

  const handleBuyNow = async () => {
    setIsLoading(true);
    try {
      // Track the buy now click
      trackBuyNumberClick({
        user_id: user?.id || user?.email,
        phone_number: phoneNumber,
      });

      const response = await createNumberPurchaseCheckoutSession(phoneNumber);
      if (response.data && response.data.checkout_url) {
        // Add a small delay to ensure the loading animation is visible
        setTimeout(() => {
          window.location.href = response.data.checkout_url;
        }, 500); // 500ms delay
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // For debugging - stringify the price object to see its exact structure
  const priceStr = JSON.stringify(phoneData?.price, null, 2);

  // Format phone number for display
  const formatPhoneNumber = (phoneNum) => {
    if (!phoneNum) return 'N/A';

    // Remove the +1 prefix for US numbers
    const cleaned = phoneNum.replace(/^\+1/, '');

    // Format as (XXX) XXX-XXXX
    if (cleaned.length === 10) {
      return `(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`;
    }

    return phoneNum; // Return as-is if we can't format it
  };

  // Format capabilities for display
  const formatCapabilities = () => {
    if (!phoneData?.capabilities) return 'None';

    const caps = [];
    if (phoneData.capabilities.voice) caps.push('Voice');
    if (phoneData.capabilities.sms) caps.push('SMS');
    if (phoneData.capabilities.mms) caps.push('MMS');

    return caps.length > 0 ? caps.join(', ') : 'None';
  };

  // Format price for display
  const formatPrice = () => {
    if (typeof phoneData?.price === 'object' && phoneData.price.monthly) {
      return `$${phoneData.price.monthly}/month`;
    } else if (typeof phoneData?.price === 'string') {
      return `$${phoneData.price}/month`;
    } else {
      return '$3.00/month'; // Default fallback
    }
  };

  // Get the actual price value for display
  const getPriceValue = () => {
    // Check if price is an object with monthly property
    if (phoneData && typeof phoneData.price === 'object' && phoneData.price.monthly) {
      return phoneData.price.monthly;
    }

    // If price is a string (unlikely but possible)
    if (phoneData && typeof phoneData.price === 'string') {
      // Try to extract a number from the string
      const match = phoneData.price.match(/(\d+(\.\d+)?)/);
      if (match) return match[0];
    }

    // Default fallback
    return "3.00";
  };

  // Format location for display
  const formatLocation = () => {
    if (!phoneData) return 'United States';

    const parts = [];
    if (phoneData?.locality) parts.push(phoneData.locality);
    if (phoneData?.region && phoneData.region !== phoneData?.iso_country) parts.push(phoneData.region);

    return parts.length > 0 ? `${parts.join(', ')}, US` : 'United States';
  };

  return (
    <DModal
      isOpen={isOpen}
      onClose={onClose}
      title="Confirm Phone Number Purchase"
      subtitle="Review the details before purchasing this phone number."
      footer={
        <div className="flex gap-size3 w-full">
          <DButton
            variant="outlined"
            size="md"
            className="flex-1"
            onClick={onClose}
          >
            Cancel
          </DButton>
          <DButton
            variant="dark"
            size="md"
            className="flex-1"
            onClick={handleBuyNow}
            loading={isLoading}
            // loadingText="Processing..."
          >
            Buy Now
          </DButton>
        </div>
      }
    >
      <div className="flex flex-col gap-size4 py-size2">
        <div className="bg-grey-5/5 p-size3 rounded-size1">
          <h3 className="text-xl font-medium mb-size3 font-mono">{phoneData?.friendly_name || phoneNumber}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-size6">
            {/* <div>
              <p className="text-sm text-grey-50 mb-size1">Phone Number</p>
              <p className="font-medium font-mono">{phoneNumber}</p>
            </div> */}
            <div>
              <p className="text-sm text-grey-75 mb-size0 font-medium">Formatted Number</p>
              <p className="font-medium font-mono">{phoneData?.friendly_name || formatPhoneNumber(phoneNumber) || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-grey-75 mb-size0 font-medium">Location</p>
              <p className="font-medium">United States</p>
            </div>
            <div>
              <p className="text-sm text-grey-75 mb-size0 font-medium">Price</p>
              <div className="flex items-center">
                <span className="font-medium text-lg">{userData.add_ons?.find(addon => addon.type === 'twilio_number') ? '$2.0' : '$0.0'}</span>
                <span className="text-sm text-grey-50 ml-1">/month</span>
              </div>
              {userData.add_ons?.find(addon => addon.type === 'twilio_number') ? <p className="text-xs text-grey-50 mt-1">Billed monthly to your account</p> : <p className="text-xs text-grey-50 mt-1 leading-[1.2]">You will need to purchase for 0$ this number via Stripe to activate it</p>}
            </div>
            <div className="">
              <p className="text-sm text-grey-75 mb-size0 font-medium">Capabilities</p>
              <div className="flex gap-2 mt-1">
                <DBadge type={STATUS.TAKEN} label="Voice" />
                <DBadge type={STATUS.TAKEN} label="SMS" />
                <DBadge type={STATUS.TAKEN} label="MMS" />
              </div>
            </div>
          </div>
        </div>

        <div className="bg-purple-5 p-size3 rounded-size1 border border-purple-200/20">
          <h4 className="font-medium mb-size2">Important Information</h4>
          <ul className="list-disc pl-size4 text-sm space-y-size1">
            <li>Phone numbers purchased through Dante AI will incur additional credits per minute when used with an AI Voice Agent.</li>
            <li>Monthly charges will be applied to your account for maintaining this phone number.</li>
            <li>You can cancel this phone number at any time from your Phone Numbers dashboard.</li>
          </ul>
        </div>
      </div>
    </DModal>
  );
};

export default PhoneNumberPurchaseModal;
