import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import LayoutMain from "@/layouts/LayoutMain";
import useToast from "@/hooks/useToast";
import { purchaseNumberCancel } from "@/services/phoneNumberPurchase.service";

const PhoneNumberPurchaseCancel = () => {
    const navigate = useNavigate();
    const { addInfoToast } = useToast();

    useEffect(() => {
        const processCancel = async () => {
            try {
                // Call the cancel endpoint to clean up any pending purchase
                await purchaseNumberCancel();
            } catch (error) {
                console.error("Error processing purchase cancellation:", error);
            } finally {
                // Show a toast and redirect to the purchase page
                addInfoToast({ message: "Phone number purchase was cancelled." });
                
                // Redirect after a short delay
                setTimeout(() => {
                    navigate("/phone-numbers/purchase");
                }, 1000);
            }
        };

        processCancel();
    }, [navigate, addInfoToast]);

    return (
        <LayoutMain title="Purchase Cancelled">
            <div className="flex flex-col items-center justify-center min-h-[50vh]">
                <p className="text-center text-grey-50">
                    Your purchase has been cancelled. Redirecting...
                </p>
            </div>
        </LayoutMain>
    );
};

export default PhoneNumberPurchaseCancel;
