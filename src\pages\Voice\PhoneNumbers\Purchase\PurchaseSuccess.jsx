import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import DLoading from "@/components/DLoading";
import LayoutMain from "@/layouts/LayoutMain";
import useToast from "@/hooks/useToast";
import { purchaseNumberSuccess } from "@/services/phoneNumberPurchase.service";
import { useUserStore } from '@/stores/user/userStore';
import { trackNumberPurchasedSuccess } from "@/helpers/analytics";

const PhoneNumberPurchaseSuccess = () => {
    const navigate = useNavigate();
    const { addSuccessToast, addErrorToast } = useToast();
    const [isProcessing, setIsProcessing] = useState(true);
    const { user } = useUserStore();

    useEffect(() => {
        const processSuccess = async () => {
            let purchasedNumber = null;

            try {
                const response = await purchaseNumberSuccess();

                if (response.success) {
                    if (response.data && (response.data.phone_number || response.phone_number)) {
                        purchasedNumber = response.data.phone_number || response.phone_number;
                        
                        // Track successful purchase
                        trackNumberPurchasedSuccess({
                            user_id: user?.id,
                            email: user?.email,
                            phone_number: purchasedNumber,
                        });
                    }
                    addSuccessToast({ message: "Phone number purchased successfully!" });
                } else {
                    addErrorToast({ message: "There was an issue confirming your purchase. Please check your account." });
                }
            } catch (error) {
                console.error("Error processing purchase success:", error);
                addErrorToast({ message: "There was an issue confirming your purchase. Please check your account." });
            } finally {
                setTimeout(() => {
                    setIsProcessing(false);
                    navigate(`/phone-numbers?just_purchased=${purchasedNumber?.replace('+', '')}`);
                    // if (purchasedNumber) {
                    // } else {
                    //     navigate("/phone-numbers");
                    // }
                }, 1500); 
            }
        };

        processSuccess();
    }, [navigate, addSuccessToast, addErrorToast, user]);

    return (
        <>
            {isProcessing ? (
                <div className="flex flex-col items-center justify-center min-h-screen bg-white">
                    <DLoading show={true} />
                    <p className="text-center mt-8 text-grey-50">
                        Activating your new phone number...
                    </p>
                </div>
            ) : (
                <LayoutMain title="Purchase Successful">
                    <div className="flex flex-col items-center justify-center min-h-[50vh]">
                        <p className="text-center text-grey-50">
                            Redirecting to your phone numbers...
                        </p>
                    </div>
                </LayoutMain>
            )}
        </>
    );
};

export default PhoneNumberPurchaseSuccess;
