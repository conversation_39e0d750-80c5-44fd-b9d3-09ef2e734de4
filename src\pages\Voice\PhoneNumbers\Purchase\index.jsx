import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import DButton from "@/components/Global/DButton";
import DTable from "@/components/Global/DTable";
import LayoutMain from "@/layouts/LayoutMain";
import DLoading from "@/components/DLoading";
import DInput from "@/components/Global/DInput/DInput";
import DSelect from "@/components/Global/DSelect";
import useToast from "@/hooks/useToast";
import { getAvailableNumbers } from "@/services/phoneNumberPurchase.service";
import PhoneNumberPurchaseModal from "./PhoneNumberPurchaseModal";
import DBadge from "@/components/Global/DBadge";
import { STATUS } from "@/constants";
import featureCheck from "@/helpers/tier/featureCheck";
import SearchIcon from "@/components/Global/Icons/SearchIcon";
import { useUserStore } from "@/stores/user/userStore";

const PhoneNumberPurchase = () => {
    const navigate = useNavigate();
    const { addSuccessToast, addErrorToast } = useToast();

    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingCheckout, setIsLoadingCheckout] = useState(false);
    const [loadingPhoneNumber, setLoadingPhoneNumber] = useState(null);
    const [availableNumbers, setAvailableNumbers] = useState([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedPhoneNumber, setSelectedPhoneNumber] = useState(null);
    const [selectedPhoneData, setSelectedPhoneData] = useState(null);
    const [rawPhoneData, setRawPhoneData] = useState([]);
    const [filters, setFilters] = useState({
        country_code: "US",
        area_code: "",
        contains: ""
    });
    const userData = useUserStore(state => state.user);

    const COUNTRIES = [
        { label: 'US', value: 'US' },
        { label: 'CA (Coming Soon)', value: 'CA', comming_soon: true, disabled: true },
        { label: 'GB (Coming Soon)', value: 'GB', comming_soon: true, disabled: true },
        { label: 'AU (Coming Soon)', value: 'AU', comming_soon: true, disabled: true },
        { label: 'NZ (Coming Soon)', value: 'NZ', comming_soon: true, disabled: true },
    ];

    const columns = [
        {
            label: 'Phone number',
            key: 'phone_number',
            showName: true,
            minWidth: '180px',
            align: 'left'
        },
        {
            label: 'Formatted',
            key: 'friendly_name',
            showName: true,
            minWidth: '150px',
            align: 'left'
        },
        {
            label: 'Location',
            key: 'location',
            showName: true,
            minWidth: '120px'
        },
        {
            label: 'Capabilities',
            key: 'capabilities',
            showName: true,
            minWidth: '200px'
        },
        {
            label: 'Price',
            key: 'price',
            showName: true,
            minWidth: '100px'
        },
        {
            label: 'Action',
            key: 'action',
            showName: true,
            minWidth: '100px'
        }
    ];

    const fetchAvailableNumbers = async () => {
        setIsLoading(true);
        try {
            // Create an API-friendly version of the filters
            const apiFilters = {
                country_code: filters.country_code,
                // Map 'area_code' to the parameter name expected by the API
                area_code: filters.area_code, // Fix: Use the correct parameter name for the API
                contains: filters.contains
            };
            
            const response = await getAvailableNumbers(apiFilters);
            if (response.data && Array.isArray(response.data)) {
                // Store the raw data for later use
                setRawPhoneData(response.data);
                const formattedNumbers = response.data.map(number => {
                    // Store the original number object in a data attribute for later use
                    return {
                        id: number.phone_number,
                        phone_number: (
                            <span className="font-mono">+1</span>
                        ),
                        friendly_name: (
                            <span className="font-mono">{number.friendly_name || 'N/A'}</span>
                        ),
                        location: (() => {
                            const parts = [];
                            if (number.locality) parts.push(number.locality);
                            if (number.region && number.region !== number.iso_country) parts.push(number.region);
                            if (parts.length === 0) return 'US';
                            return parts.join(', ');
                        })(),
                        capabilities: (
                            <div className="flex flex-wrap gap-1">
                                <DBadge type={STATUS.TAKEN} label="Voice" />
                                <DBadge type={STATUS.TAKEN} label="SMS" />
                                <DBadge type={STATUS.TAKEN} label="MMS" />
                            </div>
                        ),
                        price: (
                            <div className="flex items-center">
                                <span className="font-medium">$2.0</span>
                                <span className="text-xs text-grey-50 ml-1">/month</span>
                            </div>
                        ),
                        action: (
                            <DButton
                                size="sm"
                                variant="dark"
                                onClick={() => {
                                    if (featureCheck('buy_phone_number')) {
                                        handleBuyNumber(number.phone_number)
                                    }
                                }}
                                disabled={isLoadingCheckout}
                                loading={isLoadingCheckout && number.phone_number === loadingPhoneNumber}
                                loadingText="Processing..."
                                className="min-w-20 transition-colors duration-300 hover:bg-black/80"
                            >
                                {userData.add_ons?.find(addon => addon.type === 'twilio_number') ? 'Buy Number' : 'Buy Free Number'}
                            </DButton>
                        ),
                        // Store the original data for reference
                        originalData: number
                    };
                });
                setAvailableNumbers(formattedNumbers);
            } else {
                setAvailableNumbers([]);
                addErrorToast({ message: "No available numbers found. Try different filters." });
            }
        } catch (error) {
            console.error("Error fetching available numbers:", error);
            addErrorToast({ message: "Failed to fetch available numbers. Please try again." });
            setAvailableNumbers([]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleBuyNumber = (phoneNumber) => {
        // Find the formatted number data
        const formattedData = availableNumbers.find(item => item.id === phoneNumber);

        // Get the original data that we stored
        const originalData = formattedData?.originalData;

        // Set the selected phone number and data
        setSelectedPhoneNumber(phoneNumber);
        setSelectedPhoneData(originalData);

        // Open the modal
        setIsModalOpen(true);
    };

    useEffect(() => {
        fetchAvailableNumbers();
    }, []);

    useEffect(() => {
        if (filters.contains || filters.area_code) {
            const timer = setTimeout(() => {
                fetchAvailableNumbers();
            }, 2000);

            return () => clearTimeout(timer);
        }
    }, [filters.contains, filters.area_code]);

    const handleAreaCodeChange = (e) => {
        // Validate to ensure only numbers are entered
        const value = e.target.value.replace(/\D/g, '');
        setFilters({ ...filters, area_code: value });
    };

    if(isLoading) {
        return <DLoading show={true} />
    }

    return (
        <LayoutMain title="Purchase Phone Numbers">
            <div className="3xl:max-w-[1200px] 3xl:mx-auto">
                <p className="text-sm text-grey-75 font-light mb-size3">
                    Browse available phone numbers and purchase one for your AI Voice Agent. Phone numbers purchased through Dante AI will incur additional credits per minute when used with an AI Voice Agent.
                </p>

                <div className="bg-white p-size3 rounded-size1 mb-size3 pl-0">
                    <div className="flex flex-col md:flex-row items-end gap-size3 md:w-auto md:max-w-3xl">
                        <div className="w-full md:w-48">
                            <p className="text-sm font-medium mb-size1">Country</p>
                            <DSelect
                                options={COUNTRIES}
                                value={filters.country_code}
                                onChange={(e) => setFilters({...filters, country_code: e.target.value})}
                            />
                        </div>
                        <div className="w-full md:w-48">
                            <p className="text-sm font-medium mb-size1">Area Code</p>
                            <DInput
                                placeholder="e.g. 415"
                                value={filters.area_code}
                                onChange={handleAreaCodeChange}
                                type="tel"
                                maxLength={3}
                            />
                        </div>
                        <div className="w-full md:w-48">
                            <p className="text-sm font-medium mb-size1">Contains</p>
                            <DInput
                                placeholder="e.g. 555"
                                value={filters.contains}
                                onChange={(e) => setFilters({...filters, contains: e.target.value})}
                            />
                        </div>
                        <div className="w-full md:w-48 md:mt-6">
                            <DButton
                                size="sm"
                                variant="dark"
                                onClick={fetchAvailableNumbers}
                                className="w-full"
                            >
                                <SearchIcon className="w-6 h-6" />
                                <span className="pr-2">Search</span>
                            </DButton>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col gap-size3">
                <DTable
                    columns={columns}
                    data={availableNumbers}
                />

                {availableNumbers.length === 0 && !isLoading && (
                    <div className="text-center py-size5">
                        <p className="text-grey-50">No available numbers found. Try different filters.</p>
                    </div>
                )}

                </div>
            </div>

            {/* Purchase confirmation modal */}
            <PhoneNumberPurchaseModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                phoneNumber={selectedPhoneNumber}
                phoneData={selectedPhoneData}
                userData={userData}
            />
        </LayoutMain>
    );
};

export default PhoneNumberPurchase;
