import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import http from './http';
import { DEFAULT_HEADERS } from './constants.service';

export const createCompany = (params) => {
  return http.post(generateApiEndpoint('company'), params, {
    headers: DEFAULT_HEADERS,
  });
};

export const updateCompany = (params) => {
  return http.patch(generateApiEndpoint(`company`), params, {
    headers: DEFAULT_HEADERS,
  });
};

export const getCompany = () => {
  return http.get(generateApiEndpoint(`company`), {
    headers: DEFAULT_HEADERS,
  });
};
