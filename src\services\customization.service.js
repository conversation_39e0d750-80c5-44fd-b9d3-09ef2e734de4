import http from './http';
import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import { DEFAULT_HEADERS } from './constants.service';


export const getChatbotCustomizationById = (data) => {
  return http.get(generateApiEndpoint(`knowledge-bases/customization/`), {
    params: {
      kb_id: data.kb_id
    }
  });
};

export const getChatbotOverviewById = (kb_id) => {
  return http.get(generateApiEndpoint(`knowledge-bases/customization/${kb_id}/overview`), {
    headers: DEFAULT_HEADERS
  });
};

export const getSharedChatbotCustomizationById = async (data) => {
  return http.get(generateApiEndpoint(`knowledge-bases/customization/shared`), {
    params: {
      kb_id: data.kb_id,
      token: data.token
    }
  });
};

export const saveChatbotCustomization = async (data) => {
  return http.post(
    generateApiEndpoint(`knowledge-bases/customization/`),
    { ...data },
    {
      headers: DEFAULT_HEADERS
    }
  );
};

export const saveChatbotImages = (chatbotId, file) => {
  return http.post(
    generateApiEndpoint(`knowledge-bases/customization/upload/`),
    file,
    {
      params: { kb_id: chatbotId },
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );
};

export const updateChatbotCustomization = (kb_id, data) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/customization/${kb_id}`),
    { ...data },
    {
      headers: DEFAULT_HEADERS
    }
  );
};


export const toggleAutoRefresh = (kb_id, enabled) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/customization/${kb_id}/toggle-autorefresh`),
    { enabled },
    {
      headers: DEFAULT_HEADERS
    }
  );
};

export const toggleChatbotActivation = (kb_id, enabled) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/customization/${kb_id}/toggle-chatbot-activation`),
    { enabled },
  );
}


export const updateChatbotSafety = (kb_id, data) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/customization/${kb_id}/safety`),
    { ...data },
    {
      headers: DEFAULT_HEADERS
    }
  );
}

export const getChatbotSafety = (kb_id) => {
  return http.get(generateApiEndpoint(`knowledge-bases/customization/${kb_id}/safety`), {
    headers: DEFAULT_HEADERS
  });
}


export const updateChatbotCoreSettings = (kb_id, data) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/${kb_id}/core-settings`),
    { ...data },
  );
}

export const updateChatbotStyling = (kb_id, data) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/${kb_id}/styling`),
    { ...data },
  );
}

export const updateChatbotPowerUps = (kb_id, data) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/${kb_id}/power-ups`),
    { ...data },
  );
}

export const updateChatbotPersonality = (kb_id, data) => {
  return http.patch(
    generateApiEndpoint(`knowledge-bases/${kb_id}/personality`),
    { ...data },
  );
}
