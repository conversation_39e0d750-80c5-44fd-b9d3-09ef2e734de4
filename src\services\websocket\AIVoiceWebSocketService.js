import WebSocketService from './WebSocketService';
import { getVoiceWebSocketUrl, WS_ENDPOINT_TYPES } from './voiceWebSocketUtils';

/**
 * AIVoiceWebSocketService - A specialized WebSocket service for AI Voice functionality
 *
 * Extends the base WebSocketService with specific features for AI Voice:
 * - Handling binary audio data
 * - Authentication with token
 * - Voice-specific initialization
 */
class AIVoiceWebSocketService extends WebSocketService {
  /**
   * Create a new AIVoiceWebSocketService instance
   * @param {Object} config - Configuration options
   * @param {string} [config.url] - WebSocket URL to connect to (optional, will be generated if not provided)
   * @param {string} [config.endpointType] - Type of endpoint to use (demo, preview, browser)
   * @param {Object} config.options - Additional options
   * @param {Object} config.settings - Voice settings
   * @param {string} config.settings.authToken - Authentication token
   * @param {string} config.settings.kbId - Knowledge base ID
   * @param {string} config.settings.selectedVoice - Selected voice ID
   * @param {string} [config.settings.initialMessage] - Initial message
   * @param {string} [config.settings.userEmail] - User email
   * @param {string} [config.settings.voice_instructions] - Voice instructions
   * @param {string} [config.settings.personality_prompt] - Personality prompt for the AI Voice
   * @param {Object} callbacks - Callback functions for WebSocket events
   */
  constructor(config, callbacks = {}) {
    // Set binary type to arraybuffer for audio data
    const options = {
      ...config.options,
      binaryType: 'arraybuffer'
    };

    // Store the endpoint type for later use
    this.endpointType = config.endpointType || WS_ENDPOINT_TYPES.BROWSER;

    // Generate WebSocket URL if not provided
    const url = config.url || getVoiceWebSocketUrl(this.endpointType);

    super({ ...config, url, options }, callbacks);

    // AI Voice specific properties
    this.settings = config.settings;
    this.initSent = false;
    this.frameRef = config.frameRef;
  }

  /**
   * Connect to the WebSocket server
   * @returns {Promise<WebSocket>} A promise that resolves when the connection is established
   */
  async connect() {
    try {
      // Connect using the base class method
      const socket = await super.connect();

      // Reset initialization flag
      this.initSent = false;

      // Return the socket
      return socket;
    } catch (error) {
      console.error('Error connecting to AI Voice WebSocket:', error);
      throw error;
    }
  }

  /**
   * Send initialization data
   * @returns {boolean} Whether the initialization data was sent successfully
   */
  sendInitData() {
    if (this.initSent || !this.isConnected) {
      return false;
    }

    try {
      // Determine the appropriate auth token based on the endpoint type
      let authToken = this.settings.authToken;

      // If this is a preview or browser endpoint and we have a JWT token, use it
      if (this.endpointType === WS_ENDPOINT_TYPES.PREVIEW || this.endpointType === WS_ENDPOINT_TYPES.BROWSER) {
        // Try to get JWT token from settings or use the provided authToken as fallback
        authToken = this.settings.jwtToken || this.settings.authToken;
      } else if (this.endpointType === WS_ENDPOINT_TYPES.DEMO) {
        // For demo endpoint, use the demo token
        authToken = 'DanteAIVoiceDemo';
      }

      // Create initialization data payload
      const initData = {
        type: "dante_init",
        auth_token: authToken,
        kb_id: this.settings.kbId,
        voice_id: this.settings.selectedVoice,
        initial_message: this.settings.initialMessage || null
      };

      // Add user email if available
      if (this.settings.userEmail) {
        initData.user_email = this.settings.userEmail;
      }

      // Add voice instructions if available (for OpenAI voices)
      if (this.settings.voice_instructions) {
        initData.voice_instructions = this.settings.voice_instructions;
      }

      // Add personality prompt if available
      if (this.settings.personality_prompt) {
        initData.personality_prompt = this.settings.personality_prompt;
      }

      // Send the initialization data
      const success = this.send(JSON.stringify(initData));

      if (success) {
        this.initSent = true;
      }

      return success;
    } catch (error) {
      console.error('Error sending initialization data:', error);
      return false;
    }
  }

  /**
   * Send audio data
   * @param {Float32Array} audioData - Audio data to send
   * @param {number} sampleRate - Sample rate of the audio data
   * @param {number} numChannels - Number of channels in the audio data
   * @returns {boolean} Whether the audio data was sent successfully
   */
  sendAudioData(audioData, sampleRate, numChannels) {
    if (!this.isConnected || !this.initSent) {
      return false;
    }

    try {
      // Convert to S16 PCM format
      const pcmS16Array = this.convertFloat32ToS16PCM(audioData);
      const pcmByteArray = new Uint8Array(pcmS16Array.buffer);

      // Create and encode the frame
      const frame = this.frameRef.current.create({
        audio: {
          audio: Array.from(pcmByteArray),
          sampleRate: sampleRate,
          numChannels: numChannels
        }
      });

      // Encode the frame
      const encodedFrame = new Uint8Array(this.frameRef.current.encode(frame).finish());

      // Send the audio data
      return this.send(encodedFrame);
    } catch (error) {
      console.error('Error sending audio data:', error);
      return false;
    }
  }

  /**
   * Convert Float32Array to Int16Array (S16 PCM)
   * @private
   * @param {Float32Array} float32Array - Float32Array to convert
   * @returns {Int16Array} Converted Int16Array
   */
  convertFloat32ToS16PCM(float32Array) {
    const int16Array = new Int16Array(float32Array.length);
    for (let i = 0; i < float32Array.length; i++) {
      // Convert float32 in range [-1, 1] to int16 in range [-32768, 32767]
      const s = Math.max(-1, Math.min(1, float32Array[i]));
      int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
    }
    return int16Array;
  }

  /**
   * Override the base handleOpen method to automatically send initialization data
   * @private
   * @param {Event} event - WebSocket open event
   */
  handleOpen(event) {
    // Call the base class method
    super.handleOpen(event);

    // Send initialization data
    this.sendInitData();
  }
}

export default AIVoiceWebSocketService;
